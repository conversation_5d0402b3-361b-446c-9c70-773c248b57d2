import { PrismaService } from 'nestjs-prisma';
export declare class SessionFeedbackService {
    private prisma;
    constructor(prisma: PrismaService);
    create(dto: any): Promise<{
        message: string;
        averageRating: number;
    }>;
    private generateFeedbackMessage;
    cleanupOldFeedbacks(sessionId: number): Promise<void>;
    getSessionFeedbacks(sessionId: number): Promise<{
        studentName: string;
        studentEmail: string;
        coordinates: null;
        user: {
            id: number;
            role: import(".prisma/client").$Enums.Role;
            email: string;
            password: string;
            name: string | null;
            phone: string | null;
            profilePic: string | null;
            location: string | null;
            skills: string[];
            about: string | null;
            isActive: boolean;
            createdAt: Date;
            updatedAt: Date;
            isVerified: boolean;
            needsVerification: boolean;
            emailVerified: Date | null;
            emailVerificationCode: string | null;
            codeExpiryDate: Date | null;
            resetToken: string | null;
            resetTokenExpiry: Date | null;
        } | null;
        id: number;
        createdAt: Date;
        userId: number | null;
        rating: number;
        sessionId: number;
        comments: string | null;
    }[]>;
    getStudentFeedbacks(sessionId: number, userId: number): Promise<{
        id: number;
        sessionId: number;
        userId: number;
        rating: number | null;
        comments: string;
        feedback: string;
        sessionComments: string | null;
        trainerComments: string | null;
        teamComments: string | null;
        suggestions: string | null;
        createdAt: Date;
        studentName: string;
        studentEmail: string;
        user: {
            id: number;
            role: import(".prisma/client").$Enums.Role;
            email: string;
            password: string;
            name: string | null;
            phone: string | null;
            profilePic: string | null;
            location: string | null;
            skills: string[];
            about: string | null;
            isActive: boolean;
            createdAt: Date;
            updatedAt: Date;
            isVerified: boolean;
            needsVerification: boolean;
            emailVerified: Date | null;
            emailVerificationCode: string | null;
            codeExpiryDate: Date | null;
            resetToken: string | null;
            resetTokenExpiry: Date | null;
        };
    }[]>;
    getSessionFeedbackList(sessionId: number): Promise<{
        id: number;
        userId: number;
        studentName: string | null;
        studentEmail: string | null;
        fullFeedback: string;
        averageRating: number | null;
        emoji: string;
    }[]>;
    getStats(): Promise<{
        totalFeedbacks: number;
        averageRating: number;
        ratingDistribution: {
            '5': number;
            '4': number;
            '3': number;
            '2': number;
            '1': number;
        };
    }>;
    private getRatingDistribution;
    getAnalytics(range?: string): Promise<{
        averageRating: number;
        ratingDistribution: {
            '5': number;
            '4': number;
            '3': number;
            '2': number;
            '1': number;
        };
        timelineData: {
            date: string;
            count: number;
        }[] | {
            month: string;
            count: number;
        }[];
    }>;
    private filterByTimeRange;
    private generateTimelineData;
}
