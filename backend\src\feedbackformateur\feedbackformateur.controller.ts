import {
  Controller,
  Post,
  Get,
  Param,
  Body,
  Delete,
  Query,
  UsePipes,
  ValidationPipe,
  BadRequestException,
} from '@nestjs/common';
import { FeedbackFormateurService } from './feedbackformateur.service';
import { CreateFeedbackFormateurDto } from './dto/create-feedbackformateur.dto';

@Controller('feedback-formateur')
export class FeedbackFormateurController {
  constructor(private readonly service: FeedbackFormateurService) {}

  @Post()
  @UsePipes(new ValidationPipe({ whitelist: true }))
  async create(@Body() dto: CreateFeedbackFormateurDto) {
    return this.service.create(dto);
  }

  @Get()
  async findAll(
    @Query('formateurId') formateurId?: string,
    @Query('seanceId') seanceId?: string,
  ) {
    if (formateurId && seanceId) {
      return this.service.findAllByFormateurAndSeance(
        Number(formateurId),
        Number(seanceId),
      );
    }
    if (formateurId) {
      return this.service.findAllByFormateur(Number(formateurId));
    }
    return this.service.findAll();
  }

  @Get('seance/:seanceId')
  async findAllBySeance(@Param('seanceId') seanceId: string) {
    const feedbacks = await this.service.findAllBySeance(Number(seanceId));
    const safeFeedbacks = Array.isArray(feedbacks) ? feedbacks : [];
    // Map feedbacks to the frontend expected format
    const emojiLabels = {
      '😊': 'Satisfait',
      '👍': 'Excellent',
      '💡': 'Idées claires',
      '🚀': 'Progrès rapide',
      '🧠': 'Bonne compréhension',
      '⚠️': 'Attention nécessaire',
    };
    return safeFeedbacks.map((f: any) => ({
      id: f.id,
      studentName: f.studentName || f.etudiant?.name || f.etudiantName || '',
      studentEmail: f.studentEmail || f.etudiant?.email || f.etudiantEmail || '',
      emoji: f.emoji,
      emojiLabel: f.emojiLabel || emojiLabels[f.emoji] || '',
    }));
  }

  @Get(':id')
  async findOne(@Param('id') id: string) {
    return this.service.findOne(+id);
  }

  @Delete(':id')
  async remove(@Param('id') id: string) {
    const parsedId = Number(id);
    if (!id || isNaN(parsedId) || parsedId <= 0) {
      throw new BadRequestException('Valid id parameter is required');
    }
    return this.service.remove(parsedId);
  }

  @Delete('cleanup')
  async cleanup(
    @Body() body: { formateurId: number; seanceId: number; keepIds: number[] },
  ) {
    const { formateurId, seanceId, keepIds } = body;
    return this.service.removeNotInList(formateurId, seanceId, keepIds);
  }
}
