import { Injectable } from '@nestjs/common';
import { PrismaService } from 'nestjs-prisma';
import { CreateFeedbackFormateurDto } from './dto/create-feedbackformateur.dto';

@Injectable()
export class FeedbackFormateurService {
  constructor(private readonly prisma: PrismaService) {}

  async create(data: CreateFeedbackFormateurDto) {
    console.log('Payload reçu pour création feedback:', data);
    const etudiant = await this.prisma.user.findUnique({
      where: { id: data.etudiantId },
      select: { name: true },
    });

    const emojiLabels: Record<string, string> = {
      '😊': 'Satisfait',
      '👍': 'Excellent',
      '💡': 'Idées claires',
      '🚀': 'Progrès rapide',
      '🧠': 'Bonne compréhension',
      '⚠️': 'Attention nécessaire',
    };

    const created = await this.prisma.feedbackFormateur.create({
      data: {
        studentId: data.etudiantId,
        studentName: etudiant?.name || '',
        emoji: data.emoji,
        emojiLabel: emojiLabels[data.emoji] || '',
        commentaire: data.commentaire || '',
        formateurId: data.formateurId, // <-- bien utiliser formateurId
        seanceId: data.seanceId,       // <-- bien utiliser seanceId
      },
    });
    console.log('FeedbackFormateur créé:', created);
    return created;
  }

  async findAll() {
    const feedbacks = await this.prisma.feedbackFormateur.findMany();
    return Array.isArray(feedbacks) ? feedbacks : [];
  }

  async findOne(id: number) {
    return this.prisma.feedbackFormateur.findUnique({ where: { id } });
  }

  async findAllByFormateur(formateurId: number) {
    const feedbacks = await this.prisma.feedbackFormateur.findMany({
      where: { formateurId },
    });
    if (!Array.isArray(feedbacks)) return [];
    return await Promise.all(
      feedbacks.map(async (f) => {
        const student = await this.prisma.user.findUnique({
          where: { id: f.studentId },
          select: { email: true },
        });
        return {
          id: f.id,
          studentName: f.studentName,
          studentEmail: student?.email || '',
          emoji: f.emoji,
          emojiLabel: f.emojiLabel,
        };
      }),
    );
  }

  async findAllBySeance(seanceId: number) {
    const feedbacks = await this.prisma.feedbackFormateur.findMany({
      where: { seanceId }
    });
    if (!Array.isArray(feedbacks)) return [];
    return await Promise.all(feedbacks.map(async f => {
      const student = await this.prisma.user.findUnique({ where: { id: f.studentId }, select: { email: true } });
      return {
        id: f.id,
        studentName: f.studentName,
        studentEmail: student?.email || '',
        emoji: f.emoji,
        emojiLabel: f.emojiLabel,
      };
    }));
  }

  async findAllByFormateurAndSeance(formateurId: number, seanceId: number) {
    const feedbacks = await this.prisma.feedbackFormateur.findMany({
      where: {
        formateurId,
        seanceId,
      },
    });
    if (!Array.isArray(feedbacks)) return [];
    return await Promise.all(
      feedbacks.map(async (f) => {
        const student = await this.prisma.user.findUnique({
          where: { id: f.studentId },
          select: { email: true },
        });
        return {
          id: f.id,
          studentName: f.studentName,
          studentEmail: student?.email || '',
          emoji: f.emoji,
          emojiLabel: f.emojiLabel,
        };
      }),
    );
  }

  async remove(id: number) {
    if (!id) {
      throw new Error('ID is required for deletion');
    }
    return this.prisma.feedbackFormateur.delete({ where: { id } });
  }

  async removeNotInList(formateurId: number, seanceId: number, keepIds: number[]) {
    // Delete feedbacks for the given formateur and seance excluding the IDs in keepIds
    return this.prisma.feedbackFormateur.deleteMany({
      where: {
        formateurId,
        seanceId,
        id: {
          notIn: keepIds.length > 0 ? keepIds : [0], // if empty, delete all
        },
      },
    });
  }
}
