import { Module } from '@nestjs/common';
import { SeanceFormateurService } from './seance-formateur.service';
import { SeanceFormateurController } from './seance-formateur.controller';
import { Session2Module } from '../session2/session2.module';

@Module({
  imports: [Session2Module],
  controllers: [SeanceFormateurController],
  providers: [SeanceFormateurService],
})
export class SeanceFormateurModule {}
