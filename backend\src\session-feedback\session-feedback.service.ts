import { Injectable, BadRequestException, NotFoundException } from '@nestjs/common';
import { PrismaService } from 'nestjs-prisma';
import { User } from '@prisma/client';

@Injectable()
export class SessionFeedbackService {
  constructor(private prisma: PrismaService) {}

  async create(dto: any) {
    const { sessionId, userId, feedback, sessionComments, trainerComments, teamComments, suggestions, ratings, ...rest } = dto;
    
    // Validation des champs obligatoires
    if (!userId) throw new BadRequestException('userId requis');
    if (!sessionId) throw new BadRequestException('sessionId requis');

    // Vérification de l'existence de l'utilisateur
    const user = await this.prisma.user.findUnique({ where: { id: userId } });
    if (!user) throw new NotFoundException(`Utilisateur ${userId} introuvable`);

    // Calcul de la note moyenne
    const ratingValues: number[] = ratings ? Object.values(ratings)
      .filter((v): v is number => typeof v === 'number' && v >= 1 && v <= 5) : [];
    
    const averageRating = ratingValues.length > 0 
      ? parseFloat((ratingValues.reduce((a, b) => a + b, 0) / ratingValues.length).toFixed(1))
      : 0;

    // Vérification si un feedback existe déjà pour cette session et cet utilisateur
    const existingFeedback = await this.prisma.sessionFeedback.findFirst({ 
      where: { sessionId, userId }, 
      orderBy: { createdAt: 'desc' } 
    });

    if (existingFeedback) {
      // Mise à jour du feedback existant
      await this.prisma.sessionFeedback.update({
        where: { id: existingFeedback.id },
        data: { 
          rating: averageRating,
          comments: rest.comments,
          user: { connect: { id: userId } }
        },
      });

      await this.prisma.sessionFeedbackList.updateMany({
        where: { sessionId, userId },
        data: {
          feedback: feedback || this.generateFeedbackMessage({ ratings, ...rest }),
          nom: user.name ?? '',
          email: user.email,
          sessionComments,
          trainerComments,
          teamComments,
          suggestions,
        },
      });
    } else {
      // Création d'un nouveau feedback
      await this.prisma.$transaction([
        this.prisma.sessionFeedback.create({ 
          data: { 
            sessionId,
            userId,
            rating: averageRating,
            comments: rest.comments,
            user: { connect: { id: userId } }
          } 
        }),
        this.prisma.sessionFeedbackList.create({
          data: {
            sessionId,
            userId,
            feedback: feedback || this.generateFeedbackMessage({ ratings, ...rest }),
            nom: user.name ?? '',
            email: user.email,
            sessionComments,
            trainerComments,
            teamComments,
            suggestions,
          },
        }),
      ]);
    }

    // Nettoyage des anciens feedbacks
    await this.cleanupOldFeedbacks(sessionId);
    
    return { 
      message: 'Feedback enregistré avec succès',
      averageRating: averageRating,
    };
  }

  private generateFeedbackMessage(dto: any): string {
    const parts: string[] = [];
    
    // Ajout des évaluations
    if (dto.ratings) {
      const ratingEntries = Object.entries(dto.ratings)
        .filter(([_, value]) => typeof value === 'number' && value >= 1 && value <= 5) as [string, number][];
      
      if (ratingEntries.length > 0) {
        const avg = ratingEntries.reduce((sum: number, [_, value]) => sum + value, 0) / ratingEntries.length;
        parts.push(`Note moyenne: ${avg.toFixed(1)}/5`);
      }
    }

    // Ajout des commentaires
    if (dto.sessionComments) parts.push(`Commentaires sur la session: ${dto.sessionComments}`);
    if (dto.trainerComments) parts.push(`Commentaires sur le formateur: ${dto.trainerComments}`);
    if (dto.teamComments) parts.push(`Commentaires sur l'équipe: ${dto.teamComments}`);
    if (dto.suggestions) parts.push(`Suggestions: ${dto.suggestions}`);
    if (dto.improvementAreas) parts.push(`Zones d'amélioration: ${dto.improvementAreas}`);
    if (dto.wouldRecommend) parts.push(`Recommanderait: ${dto.wouldRecommend}`);

    return parts.join('\n\n') || 'Feedback de session soumis';
  }

  async cleanupOldFeedbacks(sessionId: number) {
    const allFeedbacks = await this.prisma.sessionFeedbackList.findMany({
      where: { sessionId },
      orderBy: { createdAt: 'desc' },
    });

    const latestMap = new Map<number, number>();
    allFeedbacks.forEach(fb => {
      if (!latestMap.has(fb.userId)) latestMap.set(fb.userId, fb.id);
    });

    const idsToDelete = allFeedbacks
      .filter(fb => latestMap.get(fb.userId) !== fb.id)
      .map(fb => fb.id);

    if (idsToDelete.length > 0) {
      await this.prisma.sessionFeedbackList.deleteMany({ 
        where: { id: { in: idsToDelete } } 
      });
    }
  }

  async getSessionFeedbacks(sessionId: number) {
    const feedbacks = await this.prisma.sessionFeedback.findMany({
      where: { sessionId },
      orderBy: { createdAt: 'desc' },
      include: { user: true },
    });

    return feedbacks.map(fb => {
      let coordinates = null;
      try {
        const commentsData = JSON.parse(fb.comments || '{}');
        if (commentsData.coordinates) {
          coordinates = commentsData.coordinates;
        }
      } catch {
        // Ignore parse errors
      }

      return {
        ...fb,
        studentName: fb.user?.name || '',
        studentEmail: fb.user?.email || '',
        coordinates,
      };
    });
  }

  async getStudentFeedbacks(sessionId: number, userId: number) {
    // Récupérer les feedbacks détaillés depuis SessionFeedbackList
    const feedbackList = await this.prisma.sessionFeedbackList.findMany({
      where: { sessionId, userId },
      orderBy: { createdAt: 'desc' },
      include: { user: true },
    });

    // Récupérer aussi les ratings depuis SessionFeedback si disponibles
    const sessionFeedbacks = await this.prisma.sessionFeedback.findMany({
      where: { sessionId, userId },
      orderBy: { createdAt: 'desc' },
      include: { user: true },
    });

    // Combiner les données des deux tables
    const combinedFeedbacks = feedbackList.map(fb => {
      // Trouver le rating correspondant dans SessionFeedback
      const matchingRating = sessionFeedbacks.find(sf =>
        Math.abs(new Date(sf.createdAt).getTime() - new Date(fb.createdAt).getTime()) < 60000 // 1 minute de différence
      );

      return {
        id: fb.id,
        sessionId: fb.sessionId,
        userId: fb.userId,
        rating: matchingRating?.rating || null,
        comments: fb.feedback,
        feedback: fb.feedback,
        sessionComments: fb.sessionComments,
        trainerComments: fb.trainerComments,
        teamComments: fb.teamComments,
        suggestions: fb.suggestions,
        createdAt: fb.createdAt,
        studentName: fb.nom || fb.user?.name || '',
        studentEmail: fb.email || fb.user?.email || '',
        user: fb.user,
      };
    });

    return combinedFeedbacks;
  }

  async getSessionFeedbackList(sessionId: number) {
    const feedbacks = await this.prisma.sessionFeedbackList.findMany({
      where: { sessionId },
      orderBy: { createdAt: 'desc' },
    });

    // Unicité par email (le plus récent)
    const uniqueFeedbacks = new Map<string, typeof feedbacks[0]>();
    feedbacks.forEach(fb => {
      if (fb.email && !uniqueFeedbacks.has(fb.email)) {
        uniqueFeedbacks.set(fb.email, fb);
      }
    });

    // Calcul des moyennes pour chaque utilisateur
    const results = await Promise.all(
      [...uniqueFeedbacks.values()].map(async (fb) => {
        const userRatings = await this.prisma.sessionFeedback.findMany({
          where: { sessionId: fb.sessionId, userId: fb.userId },
          select: { rating: true },
        });

        const validRatings = userRatings
          .map(r => r.rating)
          .filter((r): r is number => typeof r === 'number' && r >= 1 && r <= 5);

        const averageRating = validRatings.length > 0
          ? parseFloat((validRatings.reduce((a, b) => a + b, 0) / validRatings.length).toFixed(1))
          : null;

        // Ajout d'un emoji basé sur la note moyenne
        let emoji = '⭐';
        if (averageRating !== null) {
          if (averageRating >= 4.5) emoji = '🤩';
          else if (averageRating >= 3.5) emoji = '😊';
          else if (averageRating >= 2.5) emoji = '🙂';
          else if (averageRating >= 1.5) emoji = '😐';
          else emoji = '😞';
        }

        return {
          id: fb.id,
          userId: fb.userId,
          studentName: fb.nom,
          studentEmail: fb.email,
          fullFeedback: fb.feedback,
          averageRating,
          emoji,
        };
      })
    );

    return results;
  }

  async getStats() {
    const feedbacks = await this.prisma.sessionFeedback.findMany({
      include: { user: true },
    });

    const totalFeedbacks = feedbacks.length;
    const totalRating = feedbacks.reduce((sum, fb) => sum + (fb.rating || 0), 0);
    const averageRating = totalFeedbacks > 0 
      ? parseFloat((totalRating / totalFeedbacks).toFixed(1))
      : 0;

    return { 
      totalFeedbacks, 
      averageRating,
      ratingDistribution: this.getRatingDistribution(feedbacks)
    };
  }

  private getRatingDistribution(feedbacks: any[]) {
    const distribution = {
      '5': 0,
      '4': 0,
      '3': 0,
      '2': 0,
      '1': 0
    };

    feedbacks.forEach(fb => {
      if (fb.rating >= 4.5) distribution['5']++;
      else if (fb.rating >= 3.5) distribution['4']++;
      else if (fb.rating >= 2.5) distribution['3']++;
      else if (fb.rating >= 1.5) distribution['2']++;
      else if (fb.rating >= 0) distribution['1']++;
    });

    return distribution;
  }

  async getAnalytics(range = '6months') {
    const feedbacks = await this.prisma.sessionFeedback.findMany({
      include: { user: true },
    });

    const filteredFeedbacks = this.filterByTimeRange(feedbacks, range);

    return {
      averageRating: filteredFeedbacks.length > 0
        ? parseFloat((filteredFeedbacks.reduce((sum, fb) => sum + (fb.rating || 0), 0) / filteredFeedbacks.length).toFixed(1))
        : 0,
      ratingDistribution: this.getRatingDistribution(filteredFeedbacks),
      timelineData: this.generateTimelineData(filteredFeedbacks, range)
    };
  }

  private filterByTimeRange(feedbacks: any[], range: string) {
    const now = new Date();
    let cutoff: Date;

    switch (range) {
      case '7days':
        cutoff = new Date(now.setDate(now.getDate() - 7));
        break;
      case '30days':
        cutoff = new Date(now.setDate(now.getDate() - 30));
        break;
      case '3months':
        cutoff = new Date(now.setMonth(now.getMonth() - 3));
        break;
      case '6months':
        cutoff = new Date(now.setMonth(now.getMonth() - 6));
        break;
      case '1year':
        cutoff = new Date(now.setFullYear(now.getFullYear() - 1));
        break;
      default:
        return feedbacks;
    }

    return feedbacks.filter(fb => new Date(fb.createdAt) >= cutoff);
  }

  private generateTimelineData(feedbacks: any[], range: string) {
    const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
    
    if (range === '7days' || range === '30days') {
      const dailyData: Record<string, number> = {};
      const days = range === '7days' ? 7 : 30;
      
      for (let i = 0; i < days; i++) {
        const date = new Date();
        date.setDate(date.getDate() - i);
        const label = `${date.getDate()} ${months[date.getMonth()]}`;
        dailyData[label] = 0;
      }

      feedbacks.forEach(fb => {
        const date = new Date(fb.createdAt);
        const label = `${date.getDate()} ${months[date.getMonth()]}`;
        if (dailyData[label] !== undefined) dailyData[label]++;
      });

      return Object.entries(dailyData)
        .reverse()
        .map(([date, count]) => ({ date, count }));
    }

    // Par mois pour les plages plus longues
    const monthlyData: Record<string, number> = {};
    const monthCount = range === '3months' ? 3 : range === '6months' ? 6 : 12;
    
    for (let i = 0; i < monthCount; i++) {
      const date = new Date();
      date.setMonth(date.getMonth() - i);
      const label = `${months[date.getMonth()]} ${date.getFullYear()}`;
      monthlyData[label] = 0;
    }

    feedbacks.forEach(fb => {
      const date = new Date(fb.createdAt);
      const label = `${months[date.getMonth()]} ${date.getFullYear()}`;
      if (monthlyData[label] !== undefined) monthlyData[label]++;
    });

    return Object.entries(monthlyData)
      .reverse()
      .map(([month, count]) => ({ month, count }));
  }
}