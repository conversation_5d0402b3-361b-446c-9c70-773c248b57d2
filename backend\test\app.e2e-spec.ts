import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import * as request from 'supertest';
import { App } from 'supertest/types';
import { AppModule } from './../src/app.module';

import { PrismaService } from 'nestjs-prisma';

describe('AppController (e2e)', () => {
  let app: INestApplication<App>;
  let prisma: PrismaService;

  beforeEach(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = moduleFixture.createNestApplication();
    await app.init();

    prisma = app.get(PrismaService);
  });

  beforeEach(async () => {
    // Clean up feedbackList table before each test
    await prisma.feedbackList.deleteMany({});
  });

  afterAll(async () => {
    await app.close();
  });

  it('/ (GET)', () => {
    return request(app.getHttpServer())
      .get('/')
      .expect(200)
      .expect('Hello World!');
  });

  it('DELETE /feedback/cleanup-old/:seanceId - should delete older feedbacks and keep only latest per user for a seance', async () => {
    const seanceId = 1;
    // Insert multiple feedbacks for same user
    await prisma.feedbackList.createMany({
      data: [
        { id: 1, seanceId, userId: 1, createdAt: new Date('2023-01-01'), feedback: 'Feedback 1' },
        { id: 2, seanceId, userId: 1, createdAt: new Date('2023-01-02'), feedback: 'Feedback 2' },
        { id: 3, seanceId, userId: 2, createdAt: new Date('2023-01-01'), feedback: 'Feedback 3' },
        { id: 4, seanceId, userId: 2, createdAt: new Date('2023-01-03'), feedback: 'Feedback 4' },
      ],
    });

    const response = await request(app.getHttpServer())
      .delete(`/feedback/cleanup-old/${seanceId}`)
      .expect(200);

    expect(response.body).toHaveProperty('deletedCount', 2);

    // Verify only latest feedback per user remains
    const remaining = await prisma.feedbackList.findMany({ where: { seanceId } });
    expect(remaining).toHaveLength(2);
    expect(remaining.find(fb => fb.id === 2)).toBeDefined();
    expect(remaining.find(fb => fb.id === 4)).toBeDefined();
  });

  it('DELETE /feedback/cleanup-old/:seanceId - should not delete if only one feedback per user', async () => {
    const seanceId = 2;
    await prisma.feedbackList.createMany({
      data: [
        { id: 5, seanceId, userId: 3, createdAt: new Date('2023-02-01'), feedback: 'Feedback 5' },
        { id: 6, seanceId, userId: 4, createdAt: new Date('2023-02-02'), feedback: 'Feedback 6' },
      ],
    });

    const response = await request(app.getHttpServer())
      .delete(`/feedback/cleanup-old/${seanceId}`)
      .expect(200);

    expect(response.body).toHaveProperty('deletedCount', 0);

    const remaining = await prisma.feedbackList.findMany({ where: { seanceId } });
    expect(remaining).toHaveLength(2);
  });

  it('DELETE /feedback/cleanup-old/:seanceId - should handle no feedback scenario gracefully', async () => {
    const seanceId = 3;

    const response = await request(app.getHttpServer())
      .delete(`/feedback/cleanup-old/${seanceId}`)
      .expect(200);

    expect(response.body).toHaveProperty('deletedCount', 0);
  });

  it('DELETE /feedback/cleanup-old/:seanceId - should return 400 for invalid seanceId', async () => {
    const response = await request(app.getHttpServer())
      .delete('/feedback/cleanup-old/invalid')
      .expect(400);

    expect(response.body.message).toContain('seanceId is required');
  });
});
