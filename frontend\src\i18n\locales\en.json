{"common": {"add": "Add", "cancel": "Cancel", "save": "Save", "saving": "Saving...", "saveChanges": "Save Changes", "updating": "Updating...", "back": "Back", "edit": "Edit", "delete": "Delete", "programs": "Programs", "noItemsFound": "No items found", "tryAgain": "Try Again", "goHome": "Go Home", "title": "Title", "rememberMe": "Remember me", "forgotPassword": "Forgot password?", "login": "<PERSON><PERSON>", "appTitle": "Master Knowledge Academy", "logout": "Logout", "loading": "Loading...", "hide": "<PERSON>de", "details": "Details", "close": "Close", "email": "Email", "actions": "Actions", "next": "Next", "changeLanguage": "Change language", "associatedCourse": "Associated Course", "associatedModule": "Associated Module", "associatedProgram": "Associated Program", "search": "Search"}, "role": {"etudiant": "Student", "formateur": "<PERSON><PERSON><PERSON><PERSON>", "createurdeformation": "Course Creator", "etablissement": "Institution Manager", "admin": "Administrator", "student": "Student", "user": "User"}, "auth": {"emailRequired": "Email is required", "emailInvalid": "<PERSON><PERSON> is invalid", "passwordRequired": "Password is required", "passwordTooShort": "Password must be at least 6 characters", "loginFailed": "<PERSON><PERSON> failed.", "accountNotVerified": "Your account is not yet verified. Please verify by SMS.", "login": "<PERSON><PERSON>", "emailAddress": "Email address", "password": "Password", "invalidEmail": "Please enter a valid email address.", "errorOccurred": "An error occurred. Please try again.", "emailSent": "Email sent", "resetEmailSent": "If an account exists with the provided email address, you will receive an email with instructions to reset your password.", "backToLogin": "Back to login", "forgotPassword": "Forgot password", "enterEmailForReset": "Enter your email address to receive a reset link", "email": "Email", "sending": "Sending...", "sendLink": "Send link"}, "dashboard": {"adminDashboard": "Dashboard – Admin", "participants": "Participants", "participationsThisMonth": "Participations this month", "formations": "Formations", "instructors": "Instructors", "topFormations": "Top Formations (Most followed)", "students": "Students", "monthlyParticipation": "Monthly participation (12 months)", "topInstructors": "Top Instructors (student reviews)", "topPartnerInstitutions": "Top Partner Institutions"}, "notFound": {"title": "NotFound 404", "backToHome": "Back to MKA"}, "buildProgram": {"overviewTitle": "Configured Programs Overview", "searchProgram": "Search program by name", "program": "Program", "level": "Level", "published": "Published", "publish": "Publish", "unpublish": "Unpublish", "publishSuccess": "Program published successfully!", "unpublishSuccess": "Program unpublished successfully!", "publishError": "Error updating publication status.", "loadError": "Error loading programs.", "buildProgram": "Build Program", "modulesCoursesContents": "Modules, courses and contents", "programLevel": "Program level", "selectModules": "Select modules", "contents": "Contents", "basic": "Basic", "intermediate": "Intermediate", "advanced": "Advanced", "fillAllFields": "Please fill all fields", "buildSuccess": "Program built successfully!", "buildError": "Error building program"}, "content": {"contentList": "Content List", "addContent": "Add Content", "title": "Title", "type": "Type", "file": "File", "link": "Link", "actions": "Actions", "takeQuiz": "Take Quiz", "view": "View", "noFile": "No file", "confirmDelete": "Are you sure you want to delete this item?", "deleteError": "Error during deletion", "titleRequired": "Title is required", "fileRequired": "A file is required", "saveError": "Error during save", "contentType": "Content Type", "course": "Course", "exercise": "Exercise", "quiz": "Quiz", "fileType": "File Type", "image": "Image", "video": "Video", "chooseFile": "Choose <PERSON>", "selectedFile": "Selected file", "quizNote": "A quiz will be created after saving."}, "courses": {"courseList": "Course List", "addCourse": "Add Course", "actions": "Actions", "viewContent": "View Content", "confirmDelete": "Are you sure you want to delete this course?", "createError": "Error!"}, "profile": {"editProfile": "Edit Profile", "backToProfile": "Back to Profile", "clickCameraToChange": "Click camera icon to change photo", "personalInfo": "Personal Information", "fullName": "Full Name", "email": "Email", "phone": "Phone", "location": "Location", "changePassword": "Change Password", "professionalDetails": "Professional Details", "role": "Role", "contactAdminRole": "Contact admin to change role", "aboutMe": "About Me", "skillsExpertise": "Skills & Expertise", "addSkill": "<PERSON><PERSON>", "changePasswordDescription": "Please enter your current password and a new password to update your account security.", "currentPassword": "Current Password", "newPassword": "New Password", "confirmNewPassword": "Confirm New Password", "updatePassword": "Update Password", "invalidImageFile": "Please select a valid image file.", "fileSizeError": "File size must not exceed 5MB.", "blurryImageWarning": "Blurry image detected (quality: {{quality}}). Click \"Use anyway\" if you want to continue.", "goodQualityImage": "Good quality image detected (quality: {{quality}}).", "imageAnalysisError": "Error analyzing image. Please try again.", "blurryImageAccepted": "Blurry image accepted. We recommend using a sharper image for better results.", "currentPasswordRequired": "Current password is required", "newPasswordRequired": "New password is required", "passwordsDoNotMatch": "New passwords do not match", "passwordTooShort": "New password must be at least 6 characters long", "passwordChangedSuccess": "Password changed successfully!", "passwordChangeError": "Failed to change password. Please try again.", "confirmBlurryUpload": "You are about to upload a blurry image (quality: {{quality}}). This could affect your profile quality. Do you want to continue?", "profileUpdatedSuccess": "Profile updated successfully!", "profileError": "Profile Error", "userNotFound": "User not found", "analyzingImageQuality": "Analyzing image quality...", "blurryImageDetected": "Blurry image detected", "quality": "Quality", "score": "Score", "recommendSharperImage": "We recommend choosing a sharper image", "useAnyway": "Use anyway", "passwordStrength": "Password strength", "strongPasswordTips": "For a strong password, include", "atLeast8Chars": "At least 8 characters", "oneUppercase": "One uppercase letter", "oneLowercase": "One lowercase letter", "oneDigit": "One digit", "oneSpecialChar": "One special character (!@#$%...)", "passwordStrengthWeak": "weak", "passwordStrengthMedium": "medium", "passwordStrengthStrong": "strong", "missingUserData": "Missing user ID and no user data available", "invalidUserId": "Invalid user ID", "loadUserError": "Unable to load user information.", "userProfile": "User Profile", "noBio": "No bio provided", "notProvided": "Not provided", "locationNotSpecified": "Location not specified", "noSkillsAdded": "No skills added yet.", "viewProfile": "View Profile", "profileUpdatePartialError": "Profile updated but failed to upload profile picture", "profileUpdateError": "Failed to update profile", "goodImageQuality": "Good image quality", "passwordChangeEmailSent": "A confirmation email has been sent to your email address"}, "feedback": {"feedbackCenter": "Feedback Center", "submitFeedback": "Submit <PERSON>", "viewFeedback": "View Feedback", "analytics": {"title": "Feedback Analytics", "timeRange": "Time Range", "last30Days": "Last 30 Days", "last3Months": "Last 3 Months", "last6Months": "Last 6 Months", "lastYear": "Last Year", "allTime": "All Time", "ratingDistribution": "Rating Distribution", "categoryDistribution": "Category Distribution", "feedbackTimeline": "Feedback Timeline", "numberOfRatings": "Number of Ratings", "feedbackCount": "Feedback Count", "keyInsights": "Key Insights", "insight1": "Most feedback is related to Course Content (43%)", "insight2": "Average rating has improved by 0.5 stars in the last 3 months", "insight3": "Technical issues have decreased by 30% since last quarter"}, "totalFeedbacks": "Total Feedbacks", "averageRating": "Average Rating", "recentFeedback": "Recent Feedback", "pendingResponses": "Pending Responses", "categoryBreakdown": "Feedback by Category", "last7Days": "last 7 days", "categories": {"course": "Course Content", "instructor": "<PERSON><PERSON><PERSON><PERSON>", "technical": "Technical Issue", "platform": "Platform", "suggestion": "Suggestion"}, "sender": "Sender", "receiver": "Receiver", "feedbackType": "Feedback Type", "general": "General", "studentToInstructor": "Student → In<PERSON>ructor", "instructorToStudent": "In<PERSON><PERSON><PERSON> → Student", "courseFeedback": "Course Feedback", "category": "Category", "tags": "Tags", "addTags": "Add tags", "message": "Message", "rating": "Rating", "sendFeedback": "Send Feedback", "submitSuccess": "<PERSON><PERSON><PERSON> submitted successfully!", "submitError": "Failed to submit feedback. Please try again.", "search": "Search feedbacks...", "filterByType": "Filter by Type", "filterByCategory": "Filter by Category", "all": "All", "from": "From", "to": "To", "feedbackTypes": {"bug": "Bug Report", "feature": "Feature Request", "improvement": "Improvement", "complaint": "<PERSON><PERSON><PERSON><PERSON>"}, "priorities": {"low": "Low", "medium": "Medium", "high": "High", "critical": "Critical"}, "steps": {"type": "Feedback Type", "category": "Category & Priority", "description": "Detailed Description", "technical": "Technical Information", "contact": "Contact & Finalization"}, "form": {"title": "Feedback title", "description": "Detailed description", "stepsToReproduce": "Steps to reproduce", "expectedBehavior": "Expected behavior", "actualBehavior": "Actual behavior", "browser": "Browser used", "device": "Device used", "contactInfo": "Contact information", "allowContact": "I allow the team to contact me if necessary"}, "success": "Thank you for your feedback! We have received your request and will examine it as soon as possible.", "preview": "Preview of your feedback", "noFeedbackFound": "No feedback found matching your criteria", "like": "Like", "dislike": "Dislike", "report": "Report", "respond": "Respond", "reportSubmitted": "Report submitted", "showing": "Showing", "of": "of", "items": "items", "response": {"respondToFeedback": "Respond to Feedback", "yourResponse": "Your Response", "enterResponse": "Enter your response here...", "responseGuidelines": "Guidelines: Be respectful, address all points raised, and provide constructive feedback.", "sending": "Sending...", "sendResponse": "Send Response", "emptyResponseError": "Please enter a response", "submitError": "Failed to submit response. Please try again.", "responseSubmitted": "Response submitted successfully!"}}, "quiz": {"takeQuiz": "Take Quiz", "timeRemaining": "Time remaining", "hidden": "hidden", "hideTimer": "Hide timer", "showTimer": "Show timer", "question": "Question", "questionImage": "Question image", "choice": "Choice", "yourAnswer": "Your answer", "timeExpired": "Time expired! Quiz submitted automatically.", "submitQuiz": "Submit Quiz", "createQuiz": "Create Quiz", "editQuiz": "Edit Quiz", "loadError": "Error loading quiz", "loading": "Loading...", "questionText": "Question Text", "type": "Type", "score": "Score", "addImage": "Add Image", "correctAnswer": "Correct Answer", "answerChoices": "Answer Choices", "duration": "Duration (minutes)", "addQuestion": "Add Question", "saveQuiz": "Save Quiz", "imageUploadError": "Image upload error", "questionRequired": "Question must have text", "correctAnswerRequired": "Question must have at least one correct answer", "choiceTextRequired": "All choices must have text or image", "fillBlankAnswerRequired": "Fill in the blank question must have a correct answer", "quizCreatedSuccess": "Quiz created successfully!", "quizUpdatedSuccess": "Quiz updated successfully!", "submitError": "Failed to submit quiz", "addChoiceImage": "Add Image", "correctChoice": "Correct Answer", "markCorrect": "<PERSON>", "deleteChoice": "Delete this choice", "addChoice": "Add Choice", "mcq": "Multiple Choice", "trueFalse": "True/False", "fillBlank": "Fill in the Blank", "imageChoice": "Image Choice", "true": "True", "false": "False"}, "resetPassword": {"title": "Reset Password", "subtitle": "Enter a secure new password", "newPassword": "New Password", "confirmPassword": "Confirm Password", "strength": "Strength", "weak": "weak", "medium": "medium", "strong": "strong", "passwordsDoNotMatch": "Passwords do not match.", "invalidToken": "Invalid or expired link.", "successMessage": "Password updated successfully.", "errorMessage": "An error occurred. Check the link or try again.", "securityTips": "For optimal security, use", "atLeast8Chars": "At least 8 characters", "upperLowerCase": "One uppercase and one lowercase letter", "oneDigit": "One digit", "specialChar": "One special character (e.g: ! @ # $)", "resetting": "Resetting", "resetButton": "Reset Password"}, "seanceFormateur": {"title": "Manage My Sessions", "manageMySessions": "Manage My Sessions", "manageSessionsOf": "🎓 Manage My Sessions of session", "manageSessionsOfSession2": "🎓 Manage My Sessions - Session {{sessionId}} 🎓"}, "session": {"addSession": "Add cohort", "sessionList": "cohort List"}, "studentLanding": {"title": "Choose a Program", "welcome": "Welcome! Select a program below to start learning.", "noDescription": "No description provided.", "viewProgram": "View Program"}, "studentProgram": {"title": "Program Overview", "allPrograms": "All Programs", "noModules": "No modules available.", "noCourses": "No courses in this module.", "type": "Type", "viewCourse": "View Course", "completed": "Completed", "markAsDone": "<PERSON> as <PERSON>"}, "seances": {"mySessions": "My Sessions", "noSessions": "No sessions at the moment.", "program": "Program", "animateSession": "Animate session", "programDetails": "Program details", "module": "<PERSON><PERSON><PERSON>", "course": "Course", "content": "Content", "confirmDelete": "Are you sure you want to delete this session?", "uploadImageError": "Error uploading image", "uploadVideoError": "Error uploading video", "saveSuccess": "Session saved successfully!", "loadingProgram": "Loading program...", "loadingSession": "Loading session...", "hide": "<PERSON>de", "show": "Show", "hideHierarchy": "Hide hierarchy", "showHierarchy": "Show hierarchy", "sessionAdditions": "Session additions", "quizComing": "Quiz (coming soon)", "notesChat": "Notes / Chat", "whiteboard": "Whiteboard", "feedback": "<PERSON><PERSON><PERSON>", "sessionImages": "Session images", "sessionVideos": "Session videos", "sessionNotes": "Session notes", "notesPlaceholder": "Take your notes here...", "saving": "Saving...", "saveSession": "Save session", "quizFeature": "The 'Quiz' feature is coming soon!", "sessionChat": "Session chat", "writeMessage": "Write a message...", "send": "Send", "fileReady": "File ready to send", "anonymous": "Anonymous", "sessionFeedback": "Session feedback", "hideFeedback": "Hide feedback", "showFeedback": "Show feedback", "feedbackInstructions": "Click 'Show feedback' to fill out the session evaluation form", "unpublish": "Unpublish", "publish": "Publish", "statusChangeError": "Error changing status.", "fileUploadError": "File upload error", "deleteMessageError": "Cannot delete (you are not the author)", "sessionsList": "Sessions of this cohort", "refresh": "Refresh", "loading": "Loading...", "defaultRoom": "Default Room", "showMore": "Show more", "fullFeedback": "Full feedback", "studentName": "Student name", "studentEmail": "Student email", "averageRating": "Average rating", "noRating": "No rating", "veryDissatisfied": "Very dissatisfied", "dissatisfied": "Dissatisfied", "neutral": "Neutral", "satisfied": "Satisfied", "verySatisfied": "Very satisfied", "feedbackFrom": "Feedback from", "date": "Date", "sessionSection": "Session", "trainerSection": "Trainer", "teamSection": "Team", "suggestionsSection": "Suggestions", "otherSection": "Other answers", "noAnswer": "No answer", "feedbackList": "Feedback List", "feedbackDetails": "Feed<PERSON>", "noFeedbackSelected": "No feedback selected", "sessionFeedbackList": "Session Feedback List", "noFeedback": "No feedback", "generalFeedback": "General <PERSON>", "noDetailedFeedback": "No detailed feedback available", "feedbackSummary": "<PERSON><PERSON><PERSON>", "feedbackCount": "feedback(s)", "close": "Close", "export": "Export", "sessionFeedbackDetails": "Session Feedback <PERSON>", "feedbackId": "Feedback ID", "mainFeedback": "Main Feedback", "detailedComments": "Detailed Comments"}, "addSeance": {"title": "Title", "titleField": "Session Title", "date": "Date", "time": "Time", "program": "Program", "programPreview": "Program Preview", "createButton": "Create Session", "fillAllFields": "Please fill in all required fields.", "creationError": "Error creating session. Please try again.", "hideForm": "Hide form", "createNewSession": "Create new session", "createNewSessionTitle": "Create new session", "sessionPreview": "Selected session preview", "createSessionButton": "CREATE SESSION", "fillAllFieldsAlert": "Fill in all fields.", "creationErrorAlert": "Error creating session"}, "sessions": {"sessionList": "Session List", "addSession": "Create new cohort", "noSessions": "No cohorts recorded.", "publishedProgram": "Published Program", "sessionName": "cohort Name", "startDate": "Start Date", "endDate": "End Date", "uploadImage": "Upload an image", "selectedImage": "Selected image", "programPreview": "Program preview", "saveSession": "Save cohort", "fillAllFields": "Please fill in all required fields", "startBeforeEnd": "Start date must be before end date", "sessionSaved": "Session saved successfully!", "saveError": "Error saving cohort", "loadError": "Error loading cohorts", "deleteSuccess": "Session deleted successfully!", "deleteError": "Error deleting cohort", "share": "Share", "shareSession": "Share cohort", "sharePreview": "Preview of your social media post", "customizePost": "Customize your post", "choosePlatform": "Choose your platform", "copyText": "Copy text", "textCopied": "Text copied to clipboard!", "copyError": "Error copying text", "program": "Program", "period": "From", "to": "to", "unknown": "Unknown", "modulesContent": "Modules and Content", "downloadImage": "Download image", "newOpportunity": "New opportunity not to be missed!", "delete": "Delete", "join": "Join", "addUser": "Add user", "feedback": "<PERSON><PERSON><PERSON>", "hideFeedback": "<PERSON><PERSON>", "sessionFeedback": "Session Feedback", "userEmailPlaceholder": "User email", "add": "Add", "adding": "Adding...", "cancel": "Cancel", "enterEmail": "Please enter an email.", "userAdded": "User added to session!", "addUserError": "Error adding user"}, "users": {"loadError": "Error loading users", "userList": "User List", "deleteSuccess": "Account deleted successfully", "deleteError": "Error deleting account", "statusUpdateSuccess": "Account status updated successfully", "statusUpdateError": "Error updating account status", "confirmDelete": "Confirm Deletion", "confirmToggleStatus": "Are you sure you want to change this user's status?", "searchUsers": "Search users...", "totalUsers": "Total Users", "activeUsers": "Active Users", "inactiveUsers": "Inactive Users", "inactiveAccounts": "Inactive Accounts", "name": "Name", "email": "Email", "role": "Role", "status": "Status", "actions": "Actions", "active": "Active", "inactive": "Inactive", "view": "View", "edit": "Edit", "delete": "Delete", "activate": "Activate", "deactivate": "Deactivate", "refresh": "Refresh", "addUser": "Add User", "blockedAccounts": "Blocked Accounts", "pendingAccounts": "Pending Accounts", "verifiedAccounts": "Verified Accounts", "userManagement": "User Management", "adminDashboard": "Admin Dashboard", "activeAccounts": "Active Accounts", "activityRate": "Activity Rate", "searchPlaceholder": "Search users...", "noUsersFound": "No users found", "noSearchResults": "No results for this search", "startByAddingUser": "Start by adding a user", "user": "User", "deleteConfirmMessage": "Are you sure you want to delete the user", "irreversibleAction": "This action is irreversible.", "deleteButton": "Delete", "confirmToggleTitleActive": "Deactivate Account", "confirmToggleTitleInactive": "Activate Account", "confirmToggleMessageActive": "Deactivate the account of {{name}} ({{email}})?", "confirmToggleMessageInactive": "Activate the account of {{name}} ({{email}})?", "currentStatus": "Current Status", "statusActive": "Active", "statusInactive": "Inactive", "id": "ID", "usersSection": "Users Section", "invalidEmail": "Invalid email.", "emailPlaceholder": "Enter email", "phonePlaceholder": "Enter phone number", "creating": "Creating...", "assignSessions": "Sessions to assign", "noSessionsAvailable": "No sessions available", "selectMultipleSessions": "You can select one or more sessions.", "createSuccess": "User created successfully!", "emailInvalidOrUndeliverable": "Email is invalid or undeliverable.", "userAlreadyExists": "User already exists.", "createError": "Error creating user."}, "modules": {"moduleList": "Module List", "addModule": "<PERSON><PERSON>", "moduleName": "Module Name", "period": "Period", "duration": "Duration", "actions": "Actions", "viewCourses": "View Courses", "confirmDelete": "Are you sure you want to delete this module?", "periodUnit": "Period Unit", "day": "Day", "week": "Week", "month": "Month", "addSuccess": "<PERSON><PERSON><PERSON> added successfully!", "addError": "Error adding module"}, "programs": {"programList": "Program List", "programName": "Program Name", "addProgram": "Add Program", "viewProgram": "View Program", "viewPrograms": "View Programs", "confirmDelete": "Are you sure you want to delete this program?"}, "sidebar": {"home": "Home", "users": "Users", "modules": "<PERSON><PERSON><PERSON>", "courses": "Courses", "contents": "Contents", "programs": "Programs", "programsOverview": "Programs Overview", "sessions": "cohorts", "seances": "Sessions", "feedback": "<PERSON><PERSON><PERSON>"}, "table": {"noRows": "No rows", "id": "ID", "rowsPerPage": "Rows per page:"}, "countries": {"france": "France", "usa": "United States", "uk": "United Kingdom", "germany": "Germany", "spain": "Spain", "italy": "Italy", "morocco": "Morocco", "algeria": "Algeria", "tunisia": "Tunisia", "egypt": "Egypt", "saudi": "Saudi Arabia", "uae": "United Arab Emirates", "russia": "Russia", "china": "China", "japan": "Japan", "korea": "South Korea", "india": "India", "brazil": "Brazil", "mexico": "Mexico", "argentina": "Argentina", "australia": "Australia", "newzealand": "New Zealand", "southafrica": "South Africa", "nigeria": "Nigeria", "kenya": "Kenya", "turkey": "Turkey", "iran": "Iran", "pakistan": "Pakistan", "bangladesh": "Bangladesh", "vietnam": "Vietnam", "thailand": "Thailand", "singapore": "Singapore", "malaysia": "Malaysia", "indonesia": "Indonesia", "philippines": "Philippines", "belgium": "Belgium", "netherlands": "Netherlands", "switzerland": "Switzerland", "austria": "Austria", "sweden": "Sweden", "norway": "Norway", "denmark": "Denmark", "finland": "Finland", "poland": "Poland", "czech": "Czech Republic", "hungary": "Hungary", "greece": "Greece", "portugal": "Portugal", "ireland": "Ireland", "canada": "Canada"}, "skills": {"javascript": "JavaScript", "python": "Python", "java": "Java", "cpp": "C++", "csharp": "C#", "php": "PHP", "ruby": "<PERSON>", "go": "Go", "rust": "Rust", "swift": "Swift", "kotlin": "<PERSON><PERSON><PERSON>", "scala": "Scala", "r": "R", "matlab": "MATLAB", "perl": "<PERSON><PERSON>", "react": "React", "vuejs": "Vue.js", "angular": "Angular", "htmlcss": "HTML/CSS", "sass": "Sass", "less": "Less", "bootstrap": "Bootstrap", "tailwind": "Tailwind CSS", "jquery": "j<PERSON><PERSON><PERSON>", "webpack": "Webpack", "vite": "Vite", "nodejs": "Node.js", "expressjs": "Express.js", "django": "Django", "flask": "Flask", "springboot": "Spring Boot", "laravel": "<PERSON><PERSON>", "rails": "Ruby on Rails", "aspnet": "ASP.NET", "fastapi": "FastAPI", "mysql": "MySQL", "postgresql": "PostgreSQL", "mongodb": "MongoDB", "redis": "Redis", "sqlite": "SQLite", "oracle": "Oracle", "sqlserver": "SQL Server", "cassandra": "<PERSON>", "dynamodb": "DynamoDB", "firebase": "Firebase", "aws": "AWS", "azure": "Azure", "googlecloud": "Google Cloud", "docker": "<PERSON>er", "kubernetes": "Kubernetes", "jenkins": "<PERSON>", "gitlabci": "GitLab CI", "githubactions": "GitHub Actions", "terraform": "Terraform", "ansible": "Ansible", "reactnative": "React Native", "flutter": "Flutter", "iosdev": "iOS Development", "androiddev": "Android Development", "xamarin": "<PERSON><PERSON><PERSON>", "ionic": "<PERSON><PERSON>", "dataanalysis": "Data Analysis", "machinelearning": "Machine Learning", "deeplearning": "Deep Learning", "ai": "AI", "tensorflow": "TensorFlow", "pytorch": "PyTorch", "pandas": "<PERSON><PERSON>", "numpy": "NumPy", "tableau": "<PERSON><PERSON>", "powerbi": "Power BI", "uiuxdesign": "UI/UX Design", "figma": "Figma", "adobexd": "Adobe XD", "sketch": "Sketch", "photoshop": "Photoshop", "illustrator": "Illustrator", "indesign": "InDesign", "aftereffects": "After Effects", "blender": "<PERSON><PERSON>der", "digitalmarketing": "Digital Marketing", "seo": "SEO", "sem": "SEM", "socialmedia": "Social Media Marketing", "contentmarketing": "Content Marketing", "emailmarketing": "Email Marketing", "googleanalytics": "Google Analytics", "facebookads": "Facebook Ads", "projectmanagement": "Project Management", "agile": "Agile", "scrum": "Scrum", "kanban": "Ka<PERSON><PERSON>", "jira": "<PERSON><PERSON>", "trello": "Trello", "asana": "<PERSON><PERSON>", "mondaycom": "Monday.com", "slack": "<PERSON><PERSON>ck", "git": "Git", "linux": "Linux", "windowsserver": "Windows Server", "cybersecurity": "Cybersecurity", "blockchain": "Blockchain", "iot": "IoT", "apidev": "API Development", "microservices": "Microservices", "graphql": "GraphQL", "restapi": "REST API", "leadership": "Leadership", "communication": "Communication", "problemsolving": "Problem Solving", "criticalthinking": "Critical Thinking", "teammanagement": "Team Management", "publicspeaking": "Public Speaking", "negotiation": "Negotiation", "english": "English", "french": "French", "spanish": "Spanish", "german": "German", "arabic": "Arabic", "chinese": "Chinese", "japanese": "Japanese", "portuguese": "Portuguese", "italian": "Italian", "russian": "Russian", "ecommerce": "E-commerce", "fintech": "FinTech", "healthtech": "HealthTech", "edtech": "EdTech", "gaming": "Gaming", "automotive": "Automotive", "realestate": "Real Estate", "logistics": "Logistics", "retail": "Retail", "contentwriting": "Content Writing", "copywriting": "Copywriting", "technicalwriting": "Technical Writing", "blogwriting": "Blog Writing", "socialcontent": "Social Media Content", "videoediting": "Video Editing", "podcasting": "Podcasting"}, "whiteboard": {"title": "Interactive Whiteboard", "collaborativeWhiteboard": "Collaborative Whiteboard", "tools": {"pen": "Pen", "text": "Text", "table": "Table", "color": "Change Color", "undo": "Undo", "redo": "Redo"}}}