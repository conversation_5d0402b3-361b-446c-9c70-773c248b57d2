import React, { useEffect, useState } from "react";
import {
  <PERSON>,
  Typo<PERSON>,
  <PERSON>,
  Stack,
  <PERSON>ton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  IconButton,
  Chip,
  Divider,
} from "@mui/material";
import { Close as CloseIcon } from "@mui/icons-material";
import { DataGrid } from '@mui/x-data-grid';
import { Feedback as FeedbackIcon } from "@mui/icons-material";
import axios from "axios";
import { useTranslation } from 'react-i18next';
import { useParams } from "react-router-dom";

const SessionFeedbackList = () => {
  const { sessionId } = useParams();
  const { t } = useTranslation('sessions');
  const [feedbacks, setFeedbacks] = useState([]);
  const [selectedStudentFeedbacks, setSelectedStudentFeedbacks] = useState([]);
  const [feedbackDialogOpen, setFeedbackDialogOpen] = useState(false);

  const reloadFeedbacks = () => {
    if (sessionId) {
      axios.get(`http://localhost:8000/feedback/session/list/${sessionId}`)
        .then(res => {
          setFeedbacks(res.data);
        })
        .catch(err => console.error("Error loading session feedback list:", err));
    }
  };

  React.useEffect(() => {
    reloadFeedbacks();
  }, [sessionId]);

  const handleShowMore = (userId) => {
    if (sessionId && userId) {
      axios.get(`http://localhost:8000/feedback/session/${sessionId}/student/${userId}`)
        .then(res => {
          setSelectedStudentFeedbacks(res.data);
          setFeedbackDialogOpen(true);
        })
        .catch(err => console.error("Error loading all feedback for student:", err));
    }
  };

    const feedbackColumns = [
      { field: 'id', headerName: t('id'), width: 70 },
      { field: 'studentName', headerName: t('studentName'), width: 180 },
      { field: 'studentEmail', headerName: t('studentEmail'), width: 220 },
{ field: 'fullFeedback', headerName: t('fullFeedback'), width: 350, renderCell: (params) => {
        const feedback = params.value;
        if (!feedback) return <span>-</span>;

        // Simple emoji logic based on presence of positive or negative words (example)
        let emoji = '💬';
        if (feedback.toLowerCase().includes('excellent') || feedback.toLowerCase().includes('parfaite')) emoji = '🌟';
        else if (feedback.toLowerCase().includes('bon')) emoji = '👍';
        else if (feedback.toLowerCase().includes('mauvais') || feedback.toLowerCase().includes('pas')) emoji = '👎';

        const maxLength = 100;
        const isLong = feedback.length > maxLength;
        const displayText = isLong ? feedback.substring(0, maxLength) + '...' : feedback;

        return (
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <span>
              {emoji} {displayText}
            </span>
            <Button
              size="small"
              variant="outlined"
              color="primary"
              onClick={() => handleShowMore(params.row.userId)}
              sx={{
                minWidth: 'auto',
                px: 1,
                py: 0.5,
                fontSize: '0.75rem'
              }}
            >
              {t('showMore')}
            </Button>
          </Box>
        );
      }},
{ field: 'averageRating', headerName: t('averageRating'), width: 180, renderCell: (params) => {
        let avg = params.row.averageRating;
        if (avg === null || avg === undefined) return t('noRating');
        if (typeof avg === 'string') {
          avg = parseFloat(avg);
          if (isNaN(avg)) return t('noRating');
        }
        if (typeof avg !== 'number') return t('noRating');

        // Adjust emoji and label scale to match seance feedback logic
        let emoji = '😞';
        let label = t('veryDissatisfied');
        if (avg >= 4.5) {
          emoji = '🤩';
          label = t('verySatisfied');
        } else if (avg >= 3.5) {
          emoji = '😊';
          label = t('satisfied');
        } else if (avg >= 2.5) {
          emoji = '🙂';
          label = t('neutral');
        } else if (avg >= 1.5) {
          emoji = '😐';
          label = t('dissatisfied');
        }

        return (
          <span style={{ display: 'flex', alignItems: 'center', gap: 4 }}>
            <span style={{ fontSize: 22 }}>{emoji}</span>
            <span style={{ fontWeight: 'bold', marginLeft: 4 }}>{label}</span>
<span style={{ color: '#888', marginLeft: 4 }}>
  {new Intl.NumberFormat('fr-FR', { minimumFractionDigits: 2, maximumFractionDigits: 2 }).format(avg)}
</span>
          </span>
        );
      }},
    ];

  return (
    <Paper elevation={3} sx={{ p: 4, borderRadius: 4, backgroundColor: "#fefefe" }}>
      <Typography variant="h4" mb={3} fontWeight="bold" display="flex" alignItems="center" gap={1}>
        <FeedbackIcon fontSize="large" />
        {t('sessionFeedbackList')}
      </Typography>

      <Paper sx={{ p: 3 }}>
        <Box sx={{ height: 600, width: '100%' }}>
          <DataGrid
            rows={feedbacks}
            columns={feedbackColumns}
            pageSize={10}
            rowsPerPageOptions={[5, 10, 20]}
            disableSelectionOnClick
          />
        </Box>
      </Paper>

      {/* Detailed Feedback Dialog */}
      <Dialog
        open={feedbackDialogOpen}
        onClose={() => setFeedbackDialogOpen(false)}
        maxWidth="md"
        fullWidth
        sx={{
          '& .MuiDialog-paper': {
            borderRadius: 3
          }
        }}
      >
        <DialogTitle sx={{
          bgcolor: 'primary.main',
          color: 'white',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          pr: 1
        }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <FeedbackIcon fontSize="large" />
            <Box>
              <Typography variant="h5" fontWeight="bold">{t('feedbackDetails')}</Typography>
              {selectedStudentFeedbacks.length > 0 && (
                <Typography variant="body2" sx={{ opacity: 0.9 }}>
                  {selectedStudentFeedbacks[0]?.studentName || selectedStudentFeedbacks[0]?.studentEmail}
                  {selectedStudentFeedbacks[0]?.studentName && selectedStudentFeedbacks[0]?.studentEmail &&
                    ` (${selectedStudentFeedbacks[0]?.studentEmail})`
                  }
                </Typography>
              )}
            </Box>
          </Box>
          <IconButton
            onClick={() => setFeedbackDialogOpen(false)}
            sx={{ color: 'white' }}
            size="large"
          >
            <CloseIcon />
          </IconButton>
        </DialogTitle>
        <DialogContent dividers sx={{ bgcolor: "#f8fafc", maxHeight: 600, p: 3 }}>
          {selectedStudentFeedbacks.length > 0 ? (
            <Stack spacing={3}>
              {selectedStudentFeedbacks.map((fb, index) => (
                <Paper
                  key={fb.id}
                  elevation={2}
                  sx={{
                    p: 3,
                    borderRadius: 2,
                    bgcolor: 'white',
                    border: '1px solid',
                    borderColor: 'divider',
                    position: 'relative'
                  }}
                >
                  {/* Numéro du feedback */}
                  <Chip
                    label={`${t('feedback')} #${index + 1}`}
                    color="primary"
                    size="small"
                    sx={{
                      position: 'absolute',
                      top: 16,
                      right: 16,
                      fontWeight: 'bold'
                    }}
                  />

                  {/* En-tête avec date et note */}
                  <Box sx={{ mb: 3, pb: 2, borderBottom: '1px solid', borderColor: 'divider', pr: 10 }}>
                    <Stack direction="row" justifyContent="space-between" alignItems="center" flexWrap="wrap" gap={2}>
                      <Typography variant="subtitle1" color="text.secondary" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        📅 {new Date(fb.createdAt).toLocaleString('fr-FR', {
                          year: 'numeric',
                          month: 'long',
                          day: 'numeric',
                          hour: '2-digit',
                          minute: '2-digit'
                        })}
                      </Typography>
                      {fb.rating && (
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, bgcolor: 'success.main', px: 2, py: 1, borderRadius: 2 }}>
                          <Typography variant="subtitle1" color="white" fontWeight="bold">
                            ⭐ {fb.rating}/5
                          </Typography>
                        </Box>
                      )}
                    </Stack>
                  </Box>

                  {/* Informations du Session Feedback */}
                  <Box sx={{ mb: 3 }}>
                    <Typography variant="h6" color="primary" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      📋 {t('sessionFeedbackDetails')}
                    </Typography>

                    {/* Informations de base */}
                    <Paper elevation={1} sx={{ p: 2, mb: 2, bgcolor: 'info.50', borderLeft: '4px solid', borderColor: 'info.main' }}>
                      <Stack spacing={1}>
                        <Typography variant="body2" color="text.secondary">
                          <strong>{t('feedbackId')}:</strong> {fb.id}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          <strong>{t('sessionId')}:</strong> {fb.sessionId}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          <strong>{t('userId')}:</strong> {fb.userId}
                        </Typography>
                      </Stack>
                    </Paper>

                    {/* Feedback principal */}
                    {(fb.feedback || fb.comments) && (
                      <Paper elevation={1} sx={{ p: 2, mb: 2, bgcolor: 'grey.50', borderLeft: '4px solid', borderColor: 'primary.main' }}>
                        <Typography variant="subtitle2" color="primary" gutterBottom>
                          💬 {t('mainFeedback')}
                        </Typography>
                        <Typography
                          variant="body1"
                          sx={{
                            whiteSpace: 'pre-line',
                            lineHeight: 1.6,
                            color: 'text.primary'
                          }}
                        >
                          {fb.feedback || fb.comments}
                        </Typography>
                      </Paper>
                    )}
                  </Box>

                  {/* Commentaires détaillés par section */}
                  {(fb.sessionComments || fb.trainerComments || fb.teamComments || fb.suggestions) && (
                    <Box sx={{ mb: 3 }}>
                      <Typography variant="h6" color="primary" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        📝 {t('detailedComments')}
                      </Typography>

                      <Stack spacing={2}>
                        {/* Commentaires sur la session */}
                        {fb.sessionComments && (
                          <Box>
                            <Typography variant="subtitle1" color="primary" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                              🎯 {t('sessionSection')}
                            </Typography>
                            <Paper elevation={1} sx={{ p: 2, bgcolor: 'blue.50', borderLeft: '3px solid', borderColor: 'blue.main' }}>
                              <Typography variant="body2" sx={{ whiteSpace: 'pre-line', lineHeight: 1.5 }}>
                                {fb.sessionComments}
                              </Typography>
                            </Paper>
                          </Box>
                        )}

                    {/* Commentaires sur le formateur */}
                    {fb.trainerComments && (
                      <Box>
                        <Typography variant="subtitle1" color="primary" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          👨‍🏫 {t('trainerSection')}
                        </Typography>
                        <Paper elevation={1} sx={{ p: 2, bgcolor: 'green.50', borderLeft: '3px solid', borderColor: 'green.main' }}>
                          <Typography variant="body2" sx={{ whiteSpace: 'pre-line', lineHeight: 1.5 }}>
                            {fb.trainerComments}
                          </Typography>
                        </Paper>
                      </Box>
                    )}

                    {/* Commentaires sur l'équipe */}
                    {fb.teamComments && (
                      <Box>
                        <Typography variant="subtitle1" color="primary" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          👥 {t('teamSection')}
                        </Typography>
                        <Paper elevation={1} sx={{ p: 2, bgcolor: 'orange.50', borderLeft: '3px solid', borderColor: 'orange.main' }}>
                          <Typography variant="body2" sx={{ whiteSpace: 'pre-line', lineHeight: 1.5 }}>
                            {fb.teamComments}
                          </Typography>
                        </Paper>
                      </Box>
                    )}

                    {/* Suggestions */}
                    {fb.suggestions && (
                      <Box>
                        <Typography variant="subtitle1" color="primary" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          💡 {t('suggestionsSection')}
                        </Typography>
                        <Paper elevation={1} sx={{ p: 2, bgcolor: 'purple.50', borderLeft: '3px solid', borderColor: 'purple.main' }}>
                          <Typography variant="body2" sx={{ whiteSpace: 'pre-line', lineHeight: 1.5 }}>
                            {fb.suggestions}
                          </Typography>
                        </Paper>
                      </Box>
                    )}
                      </Stack>
                    </Box>
                  )}

                  {/* Message si aucun détail */}
                  {!fb.feedback && !fb.comments && !fb.sessionComments && !fb.trainerComments && !fb.teamComments && !fb.suggestions && (
                    <Box sx={{ textAlign: 'center', py: 3 }}>
                      <Typography variant="body2" color="text.secondary">
                        {t('noDetailedFeedback')}
                      </Typography>
                    </Box>
                  )}
                </Paper>
              ))}
            </Stack>
          ) : (
            <Box sx={{ textAlign: 'center', py: 4 }}>
              <Typography variant="h6" color="text.secondary">
                {t('noFeedbackSelected')}
              </Typography>
            </Box>
          )}
        </DialogContent>

        <DialogActions sx={{ p: 3, bgcolor: 'grey.50' }}>
          <Button
            onClick={() => setFeedbackDialogOpen(false)}
            variant="outlined"
            color="primary"
          >
            {t('close')}
          </Button>
          <Button
            onClick={() => {
              // Fonctionnalité future : exporter ou imprimer
              console.log('Export feedback:', selectedStudentFeedbacks);
            }}
            variant="contained"
            color="primary"
            disabled
          >
            {t('export')} (à venir)
          </Button>
        </DialogActions>
      </Dialog>
    </Paper>
  );
};

export default SessionFeedbackList;
