import React, { useEffect, useState } from "react";
import {
  <PERSON>,
  Typography,
  Paper,
  Divider,
  <PERSON>ack,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
} from "@mui/material";
import { DataGrid } from '@mui/x-data-grid';
import { Feedback as FeedbackIcon } from "@mui/icons-material";
import axios from "axios";
import { useTranslation } from 'react-i18next';
import { useParams } from "react-router-dom";

const SessionFeedbackList = () => {
  const { sessionId } = useParams();
  const { t } = useTranslation('sessions');
  const [feedbacks, setFeedbacks] = useState([]);
  const [selectedStudentFeedbacks, setSelectedStudentFeedbacks] = useState([]);
  const [feedbackDialogOpen, setFeedbackDialogOpen] = useState(false);

  const reloadFeedbacks = () => {
    if (sessionId) {
      axios.get(`http://localhost:8000/feedback/session/list/${sessionId}`)
        .then(res => {
          setFeedbacks(res.data);
        })
        .catch(err => console.error("Error loading session feedback list:", err));
    }
  };

  React.useEffect(() => {
    reloadFeedbacks();
  }, [sessionId]);

  const handleShowMore = (userId) => {
    if (sessionId && userId) {
      axios.get(`http://localhost:8000/feedback/session/${sessionId}/student/${userId}`)
        .then(res => {
          setSelectedStudentFeedbacks(res.data);
          setFeedbackDialogOpen(true);
        })
        .catch(err => console.error("Error loading all feedback for student:", err));
    }
  };

    const feedbackColumns = [
      { field: 'id', headerName: t('id'), width: 70 },
      { field: 'studentName', headerName: t('studentName'), width: 180 },
      { field: 'studentEmail', headerName: t('studentEmail'), width: 220 },
{ field: 'fullFeedback', headerName: t('fullFeedback'), width: 350, renderCell: (params) => {
        const feedback = params.value;
        if (!feedback) return <span>-</span>;

        // Simple emoji logic based on presence of positive or negative words (example)
        let emoji = '💬';
        if (feedback.toLowerCase().includes('excellent') || feedback.toLowerCase().includes('parfaite')) emoji = '🌟';
        else if (feedback.toLowerCase().includes('bon')) emoji = '👍';
        else if (feedback.toLowerCase().includes('mauvais') || feedback.toLowerCase().includes('pas')) emoji = '👎';

        const maxLength = 100;
        const isLong = feedback.length > maxLength;
        const displayText = isLong ? feedback.substring(0, maxLength) + '...' : feedback;

        return (
          <span>
            {emoji} {displayText}
            {isLong && (
              <Button
                size="small"
                variant="text"
                onClick={() => handleShowMore(params.row.id)}
              >
                {t('showMore')}
              </Button>
            )}
          </span>
        );
      }},
{ field: 'averageRating', headerName: t('averageRating'), width: 180, renderCell: (params) => {
        let avg = params.row.averageRating;
        if (avg === null || avg === undefined) return t('noRating');
        if (typeof avg === 'string') {
          avg = parseFloat(avg);
          if (isNaN(avg)) return t('noRating');
        }
        if (typeof avg !== 'number') return t('noRating');

        // Adjust emoji and label scale to match seance feedback logic
        let emoji = '😞';
        let label = t('veryDissatisfied');
        if (avg >= 4.5) {
          emoji = '🤩';
          label = t('verySatisfied');
        } else if (avg >= 3.5) {
          emoji = '😊';
          label = t('satisfied');
        } else if (avg >= 2.5) {
          emoji = '🙂';
          label = t('neutral');
        } else if (avg >= 1.5) {
          emoji = '😐';
          label = t('dissatisfied');
        }

        return (
          <span style={{ display: 'flex', alignItems: 'center', gap: 4 }}>
            <span style={{ fontSize: 22 }}>{emoji}</span>
            <span style={{ fontWeight: 'bold', marginLeft: 4 }}>{label}</span>
<span style={{ color: '#888', marginLeft: 4 }}>
  {new Intl.NumberFormat('fr-FR', { minimumFractionDigits: 2, maximumFractionDigits: 2 }).format(avg)}
</span>
          </span>
        );
      }},
    ];

  return (
    <Paper elevation={3} sx={{ p: 4, borderRadius: 4, backgroundColor: "#fefefe" }}>
      <Typography variant="h4" mb={3} fontWeight="bold" display="flex" alignItems="center" gap={1}>
        <FeedbackIcon fontSize="large" />
        {t('sessionFeedbackList')}
      </Typography>

      <Paper sx={{ p: 3 }}>
        <Box sx={{ height: 600, width: '100%' }}>
          <DataGrid
            rows={feedbacks}
            columns={feedbackColumns}
            pageSize={10}
            rowsPerPageOptions={[5, 10, 20]}
            disableSelectionOnClick
          />
        </Box>
      </Paper>

      {/* Detailed Feedback Dialog */}
      <Dialog open={feedbackDialogOpen} onClose={() => setFeedbackDialogOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>
          {t('feedbackDetails')}
        </DialogTitle>
        <DialogContent dividers sx={{ bgcolor: "#f8fafc", maxHeight: 500 }}>
          {selectedStudentFeedbacks.length > 0 ? (
            <Box>
              {selectedStudentFeedbacks.map((fb, index) => (
                <Box key={fb.id} mb={index < selectedStudentFeedbacks.length - 1 ? 3 : 0}>
                  <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
                    {fb.studentName} ({fb.studentEmail}) - {new Date(fb.createdAt).toLocaleString()}
                  </Typography>
                  <Typography variant="body2" style={{ whiteSpace: 'pre-line' }}>
                    {fb.feedback || t('noFeedback')}
                  </Typography>
                  <Divider sx={{ my: 2 }} />
                </Box>
              ))}
            </Box>
          ) : (
            <Typography>{t('noFeedbackSelected')}</Typography>
          )}
        </DialogContent>
      </Dialog>
    </Paper>
  );
};

export default SessionFeedbackList;
