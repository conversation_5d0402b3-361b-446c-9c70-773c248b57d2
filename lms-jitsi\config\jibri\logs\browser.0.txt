Jibri 2025-07-19 12:05:59.117 INFO: [63] JibriSelenium.leaveCallAndQuitBrowser#349: Logs for call null
Jibri 2025-07-19 12:05:59.134 INFO: [63] JibriSelenium.leaveCallAndQuitBrowser#354: ========= TYPE=browser ===========
Jibri 2025-07-19 12:05:59.155 INFO: [63] JibriSelenium.leaveCallAndQuitBrowser#354: ========= TYPE=driver ===========
Jibri 2025-07-19 12:05:59.155 INFO: [63] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-19T12:05:58+0000] [INFO] Browser search. Trying... /usr/bin/chrome

Jibri 2025-07-19 12:05:59.157 INFO: [63] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-19T12:05:58+0000] [INFO] Browser search. Trying... /usr/bin/chrome

Jibri 2025-07-19 12:05:59.158 INFO: [63] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-19T12:05:58+0000] [INFO] Browser search. Trying... /usr/bin/google-chrome

Jibri 2025-07-19 12:05:59.158 INFO: [63] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-19T12:05:58+0000] [INFO] Browser search. Found at  /usr/bin/google-chrome

Jibri 2025-07-19 12:05:59.159 INFO: [63] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-19T12:05:58+0000] [INFO] Populating Preferences file: {
   "alternate_error_pages": {
      "enabled": false
   },
   "autofill": {
      "enabled": false
   },
   "browser": {
      "check_default_browser": false
   },
   "distribution": {
      "import_bookmarks": false,
      "import_history": false,
      "import_search_engine": false,
      "make_chrome_default_for_user": false,
      "skip_first_run_ui": true
   },
   "dns_prefetching": {
      "enabled": false
   },
   "profile": {
      "content_settings": {
         "pattern_pairs": {
            "https://*,*": {
               "media-stream": {
                  "audio": "Default",
                  "video": "Default"
               }
            }
         }
      },
      "default_content_setting_values": {
         "geolocation": 1
      },
      "default_content_settings": {
         "geolocation": 1,
         "mouselock": 1,
         "notifications": 1,
         "popups": 1,
         "ppapi-broker": 1
      },
      "password_manager_enabled": false
   },
   "safebrowsing": {
      "enabled": false
   },
   "search": {
      "suggest_enabled": false
   },
   "translate": {
      "enabled": false
   }
}

Jibri 2025-07-19 12:05:59.159 INFO: [63] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-19T12:05:58+0000] [INFO] Populating Local State file: {
   "background_mode": {
      "enabled": false
   },
   "ssl": {
      "rev_checking": {
         "enabled": false
      }
   }
}

Jibri 2025-07-19 12:05:59.159 INFO: [63] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-19T12:05:58+0000] [INFO] ChromeDriver supports communication with Chrome via pipes. This is more reliable and more secure.

Jibri 2025-07-19 12:05:59.159 INFO: [63] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-19T12:05:58+0000] [INFO] Use the --remote-debugging-pipe Chrome switch instead of the default --remote-debugging-port to enable this communication mode.

Jibri 2025-07-19 12:05:59.160 INFO: [63] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-19T12:05:58+0000] [INFO] Launching chrome: /usr/bin/google-chrome --allow-pre-commit-input --autoplay-policy=no-user-gesture-required --disable-background-networking --disable-client-side-phishing-detection --disable-default-apps --disable-hang-monitor --disable-popup-blocking --disable-prompt-on-repost --disable-sync --enable-automation --enable-logging --enabled --kiosk --log-level=0 --no-first-run --no-service-autorun --password-store=basic --remote-debugging-port=0 --start-maximized --test-type=webdriver --use-fake-ui-for-media-stream --use-mock-keychain --user-data-dir=/tmp/.org.chromium.Chromium.DRdcGT data:,

Jibri 2025-07-19 12:05:59.160 INFO: [63] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-19T12:05:58+0000] [FINE] DevTools HTTP Request: http://localhost:45323/json/version

Jibri 2025-07-19 12:05:59.160 INFO: [63] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-19T12:05:58+0000] [FINE] DevTools HTTP Response: {
   "Browser": "Chrome/130.0.6723.116",
   "Protocol-Version": "1.3",
   "User-Agent": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36",
   "V8-Version": "***********",
   "WebKit-Version": "537.36 (@6ac35f94ae3d01152cf1946c896b0678e48f8ec4)",
   "webSocketDebuggerUrl": "ws://localhost:45323/devtools/browser/abfa190b-b2c1-45d8-b8d0-d498df85b44a"
}


Jibri 2025-07-19 12:05:59.160 INFO: [63] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-19T12:05:58+0000] [FINE] DevTools HTTP Request: http://localhost:45323/json/list

Jibri 2025-07-19 12:05:59.161 INFO: [63] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-19T12:05:58+0000] [FINE] DevTools HTTP Response: [ {
   "description": "",
   "devtoolsFrontendUrl": "/devtools/inspector.html?ws=localhost:45323/devtools/page/2E347B48361BBEE07054DF64D237A35E",
   "id": "2E347B48361BBEE07054DF64D237A35E",
   "title": "",
   "type": "page",
   "url": "data:,",
   "webSocketDebuggerUrl": "ws://localhost:45323/devtools/page/2E347B48361BBEE07054DF64D237A35E"
} ]


Jibri 2025-07-19 12:05:59.161 INFO: [63] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-19T12:05:58+0000] [FINE] DevTools WebSocket Command: Target.getTargets (id=1) (session_id=) browser {
}

Jibri 2025-07-19 12:05:59.161 INFO: [63] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-19T12:05:58+0000] [FINE] DevTools WebSocket Response: Target.getTargets (id=1) (session_id=) browser {
   "targetInfos": [ {
      "attached": false,
      "browserContextId": "0FBE9B614639B13A1B291972A1787414",
      "canAccessOpener": false,
      "targetId": "2E347B48361BBEE07054DF64D237A35E",
      "title": "",
      "type": "page",
      "url": "data:,"
   } ]
}

Jibri 2025-07-19 12:05:59.161 INFO: [63] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-19T12:05:58+0000] [FINE] DevTools WebSocket Command: Target.attachToTarget (id=2) (session_id=) browser {
   "flatten": true,
   "targetId": "2E347B48361BBEE07054DF64D237A35E"
}

Jibri 2025-07-19 12:05:59.161 INFO: [63] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-19T12:05:58+0000] [FINE] DevTools WebSocket Event: Target.attachedToTarget (session_id=) browser {
   "sessionId": "EB2788A58B4D0F624CE3D701554EA77B",
   "targetInfo": {
      "attached": true,
      "browserContextId": "0FBE9B614639B13A1B291972A1787414",
      "canAccessOpener": false,
      "targetId": "2E347B48361BBEE07054DF64D237A35E",
      "title": "",
      "type": "page",
      "url": "data:,"
   },
   "waitingForDebugger": false
}

Jibri 2025-07-19 12:05:59.162 INFO: [63] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-19T12:05:58+0000] [FINE] DevTools WebSocket Response: Target.attachToTarget (id=2) (session_id=) browser {
   "sessionId": "EB2788A58B4D0F624CE3D701554EA77B"
}

Jibri 2025-07-19 12:05:59.162 INFO: [63] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-19T12:05:58+0000] [FINE] DevTools WebSocket Command: Page.enable (id=3) (session_id=EB2788A58B4D0F624CE3D701554EA77B) 2E347B48361BBEE07054DF64D237A35E {
}

Jibri 2025-07-19 12:05:59.162 INFO: [63] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-19T12:05:58+0000] [FINE] DevTools WebSocket Command: Page.addScriptToEvaluateOnNewDocument (id=4) (session_id=EB2788A58B4D0F624CE3D701554EA77B) 2E347B48361BBEE07054DF64D237A35E {
   "source": "(function () {window.cdc_adoQpoasnfa76pfcZLmcfl_Array = window.Array;window.cdc_adoQpoasnfa76pfcZLmcfl_Object = window.Object;window.cdc_adoQpoasnfa76pfcZLmcfl_Promise = window.Promise;window.cdc_a..."
}

Jibri 2025-07-19 12:05:59.162 INFO: [63] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-19T12:05:58+0000] [FINE] DevTools WebSocket Command: Runtime.evaluate (id=5) (session_id=EB2788A58B4D0F624CE3D701554EA77B) 2E347B48361BBEE07054DF64D237A35E {
   "expression": "(function () {window.cdc_adoQpoasnfa76pfcZLmcfl_Array = window.Array;window.cdc_adoQpoasnfa76pfcZLmcfl_Object = window.Object;window.cdc_adoQpoasnfa76pfcZLmcfl_Promise = window.Promise;window.cdc_a..."
}

Jibri 2025-07-19 12:05:59.162 INFO: [63] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-19T12:05:58+0000] [FINE] DevTools WebSocket Command: Log.enable (id=6) (session_id=EB2788A58B4D0F624CE3D701554EA77B) 2E347B48361BBEE07054DF64D237A35E {
}

Jibri 2025-07-19 12:05:59.163 INFO: [63] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-19T12:05:58+0000] [FINE] DevTools WebSocket Command: Target.setAutoAttach (id=7) (session_id=EB2788A58B4D0F624CE3D701554EA77B) 2E347B48361BBEE07054DF64D237A35E {
   "autoAttach": true,
   "flatten": true,
   "waitForDebuggerOnStart": false
}

Jibri 2025-07-19 12:05:59.163 INFO: [63] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-19T12:05:58+0000] [FINE] DevTools WebSocket Response: Page.enable (id=3) (session_id=EB2788A58B4D0F624CE3D701554EA77B) 2E347B48361BBEE07054DF64D237A35E {
}

Jibri 2025-07-19 12:05:59.163 INFO: [63] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-19T12:05:58+0000] [FINE] DevTools WebSocket Response: Page.addScriptToEvaluateOnNewDocument (id=4) (session_id=EB2788A58B4D0F624CE3D701554EA77B) 2E347B48361BBEE07054DF64D237A35E {
   "identifier": "1"
}

Jibri 2025-07-19 12:05:59.163 INFO: [63] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-19T12:05:58+0000] [FINE] DevTools WebSocket Response: Runtime.evaluate (id=5) (session_id=EB2788A58B4D0F624CE3D701554EA77B) 2E347B48361BBEE07054DF64D237A35E {
   "result": {
      "type": "undefined"
   }
}

Jibri 2025-07-19 12:05:59.163 INFO: [63] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-19T12:05:58+0000] [FINE] DevTools WebSocket Response: Log.enable (id=6) (session_id=EB2788A58B4D0F624CE3D701554EA77B) 2E347B48361BBEE07054DF64D237A35E {
}

Jibri 2025-07-19 12:05:59.164 INFO: [63] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-19T12:05:58+0000] [FINE] DevTools WebSocket Response: Target.setAutoAttach (id=7) (session_id=EB2788A58B4D0F624CE3D701554EA77B) 2E347B48361BBEE07054DF64D237A35E {
}

Jibri 2025-07-19 12:05:59.164 INFO: [63] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-19T12:05:58+0000] [FINE] DevTools WebSocket Command: Runtime.enable (id=8) (session_id=EB2788A58B4D0F624CE3D701554EA77B) 2E347B48361BBEE07054DF64D237A35E {
}

Jibri 2025-07-19 12:05:59.164 INFO: [63] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-19T12:05:58+0000] [FINE] DevTools WebSocket Event: Page.domContentEventFired (session_id=EB2788A58B4D0F624CE3D701554EA77B) 2E347B48361BBEE07054DF64D237A35E {
   "timestamp": 642.247379
}

Jibri 2025-07-19 12:05:59.165 INFO: [63] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-19T12:05:58+0000] [FINE] DevTools WebSocket Event: Page.loadEventFired (session_id=EB2788A58B4D0F624CE3D701554EA77B) 2E347B48361BBEE07054DF64D237A35E {
   "timestamp": 642.247779
}

Jibri 2025-07-19 12:05:59.165 INFO: [63] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-19T12:05:58+0000] [FINE] DevTools WebSocket Event: Page.frameStoppedLoading (session_id=EB2788A58B4D0F624CE3D701554EA77B) 2E347B48361BBEE07054DF64D237A35E {
   "frameId": "2E347B48361BBEE07054DF64D237A35E"
}

Jibri 2025-07-19 12:05:59.166 INFO: [63] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-19T12:05:58+0000] [FINE] DevTools WebSocket Event: Page.frameResized (session_id=EB2788A58B4D0F624CE3D701554EA77B) 2E347B48361BBEE07054DF64D237A35E {
}

Jibri 2025-07-19 12:05:59.166 INFO: [63] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-19T12:05:58+0000] [FINE] DevTools WebSocket Event: Runtime.executionContextCreated (session_id=EB2788A58B4D0F624CE3D701554EA77B) 2E347B48361BBEE07054DF64D237A35E {
   "context": {
      "auxData": {
         "frameId": "2E347B48361BBEE07054DF64D237A35E",
         "isDefault": true,
         "type": "default"
      },
      "id": 1,
      "name": "",
      "origin": "://",
      "uniqueId": "-5193340230010790393.1044575070816842773"
   }
}

Jibri 2025-07-19 12:05:59.167 INFO: [63] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-19T12:05:58+0000] [FINE] DevTools WebSocket Response: Runtime.enable (id=8) (session_id=EB2788A58B4D0F624CE3D701554EA77B) 2E347B48361BBEE07054DF64D237A35E {
}

Jibri 2025-07-19 12:05:59.167 INFO: [63] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-19T12:05:58+0000] [FINE] DevTools WebSocket Command: Runtime.enable (id=9) (session_id=EB2788A58B4D0F624CE3D701554EA77B) 2E347B48361BBEE07054DF64D237A35E {
}

Jibri 2025-07-19 12:05:59.167 INFO: [63] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-19T12:05:58+0000] [FINE] DevTools WebSocket Response: Runtime.enable (id=9) (session_id=EB2788A58B4D0F624CE3D701554EA77B) 2E347B48361BBEE07054DF64D237A35E {
}

Jibri 2025-07-19 12:05:59.167 INFO: [63] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-19T12:05:58+0000] [INFO] [c6fb51a812c0875958b82105a25392bd] RESPONSE InitSession {
   "acceptInsecureCerts": false,
   "acceptSslCerts": false,
   "browserConnectionEnabled": false,
   "browserName": "chrome",
   "chrome": {
      "chromedriverVersion": "130.0.6723.116 (6ac35f94ae3d01152cf1946c896b0678e48f8ec4-refs/branch-heads/6723@{#1764})",
      "userDataDir": "/tmp/.org.chromium.Chromium.DRdcGT"
   },
   "cssSelectorsEnabled": true,
   "databaseEnabled": false,
   "fedcm:accounts": true,
   "goog:chromeOptions": {
      "debuggerAddress": "localhost:45323"
   },
   "handlesAlerts": true,
   "hasTouchScreen": false,
   "javascriptEnabled": true,
   "locationContextEnabled": true,
   "mobileEmulationEnabled": false,
   "nativeEvents": true,
   "networkConnectionEnabled": false,
   "pageLoadStrategy": "normal",
   "platform": "Linux",
   "proxy": {
   },
   "~~~": "..."
}

Jibri 2025-07-19 12:05:59.168 INFO: [63] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-19T12:05:58+0000] [INFO] [c6fb51a812c0875958b82105a25392bd] COMMAND SetTimeouts {
   "ms": 60000,
   "type": "page load"
}

Jibri 2025-07-19 12:05:59.168 INFO: [63] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-19T12:05:58+0000] [INFO] [c6fb51a812c0875958b82105a25392bd] RESPONSE SetTimeouts

Jibri 2025-07-19 12:05:59.168 INFO: [63] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-19T12:05:58+0000] [INFO] [c6fb51a812c0875958b82105a25392bd] COMMAND Navigate {
   "url": "https://localhost:8443"
}

Jibri 2025-07-19 12:05:59.168 INFO: [63] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-19T12:05:58+0000] [INFO] Waiting for pending navigations...

Jibri 2025-07-19 12:05:59.169 INFO: [63] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-19T12:05:58+0000] [FINE] DevTools WebSocket Command: Runtime.evaluate (id=10) (session_id=EB2788A58B4D0F624CE3D701554EA77B) 2E347B48361BBEE07054DF64D237A35E {
   "expression": "1"
}

Jibri 2025-07-19 12:05:59.169 INFO: [63] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-19T12:05:58+0000] [FINE] DevTools WebSocket Response: Runtime.evaluate (id=10) (session_id=EB2788A58B4D0F624CE3D701554EA77B) 2E347B48361BBEE07054DF64D237A35E {
   "result": {
      "description": "1",
      "type": "number",
      "value": 1
   }
}

Jibri 2025-07-19 12:05:59.169 INFO: [63] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-19T12:05:58+0000] [INFO] Done waiting for pending navigations. Status: ok

Jibri 2025-07-19 12:05:59.169 INFO: [63] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-19T12:05:58+0000] [FINE] DevTools WebSocket Command: Page.navigate (id=11) (session_id=EB2788A58B4D0F624CE3D701554EA77B) 2E347B48361BBEE07054DF64D237A35E {
   "url": "https://localhost:8443"
}

Jibri 2025-07-19 12:05:59.169 INFO: [63] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-19T12:05:58+0000] [FINE] DevTools WebSocket Event: Page.frameStartedLoading (session_id=EB2788A58B4D0F624CE3D701554EA77B) 2E347B48361BBEE07054DF64D237A35E {
   "frameId": "2E347B48361BBEE07054DF64D237A35E"
}

Jibri 2025-07-19 12:05:59.170 INFO: [63] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-19T12:05:58+0000] [FINE] DevTools WebSocket Response: Page.navigate (id=11) (session_id=EB2788A58B4D0F624CE3D701554EA77B) 2E347B48361BBEE07054DF64D237A35E {
   "errorText": "net::ERR_CONNECTION_REFUSED",
   "frameId": "2E347B48361BBEE07054DF64D237A35E",
   "loaderId": "1C84AD6DED1D4FD657A84C01CB333B5A"
}

Jibri 2025-07-19 12:05:59.170 INFO: [63] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-19T12:05:58+0000] [FINE] DevTools WebSocket Command: Runtime.evaluate (id=12) (session_id=EB2788A58B4D0F624CE3D701554EA77B) 2E347B48361BBEE07054DF64D237A35E {
   "expression": "1"
}

Jibri 2025-07-19 12:05:59.170 INFO: [63] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-19T12:05:58+0000] [FINE] DevTools WebSocket Event: Runtime.executionContextsCleared (session_id=EB2788A58B4D0F624CE3D701554EA77B) 2E347B48361BBEE07054DF64D237A35E {
}

Jibri 2025-07-19 12:05:59.170 INFO: [63] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-19T12:05:58+0000] [FINE] DevTools WebSocket Event: Runtime.executionContextsCleared (session_id=EB2788A58B4D0F624CE3D701554EA77B) 2E347B48361BBEE07054DF64D237A35E {
}

Jibri 2025-07-19 12:05:59.170 INFO: [63] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-19T12:05:58+0000] [FINE] DevTools WebSocket Event: Page.frameNavigated (session_id=EB2788A58B4D0F624CE3D701554EA77B) 2E347B48361BBEE07054DF64D237A35E {
   "frame": {
      "adFrameStatus": {
         "adFrameType": "none"
      },
      "crossOriginIsolatedContextType": "NotIsolated",
      "domainAndRegistry": "",
      "gatedAPIFeatures": [  ],
      "id": "2E347B48361BBEE07054DF64D237A35E",
      "loaderId": "B01249162E66A8BF19018CE0190EF739",
      "mimeType": "text/html",
      "secureContextType": "InsecureScheme",
      "securityOrigin": "://",
      "unreachableUrl": "https://localhost:8443/",
      "url": "chrome-error://chromewebdata/"
   },
   "type": "Navigation"
}

Jibri 2025-07-19 12:05:59.171 INFO: [63] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-19T12:05:58+0000] [FINE] DevTools WebSocket Event: Runtime.executionContextCreated (session_id=EB2788A58B4D0F624CE3D701554EA77B) 2E347B48361BBEE07054DF64D237A35E {
   "context": {
      "auxData": {
         "frameId": "2E347B48361BBEE07054DF64D237A35E",
         "isDefault": true,
         "type": "default"
      },
      "id": 1,
      "name": "",
      "origin": "://",
      "uniqueId": "-5446052259107647406.-6584951771831250357"
   }
}

Jibri 2025-07-19 12:05:59.171 INFO: [63] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-19T12:05:58+0000] [FINE] DevTools WebSocket Response: Runtime.evaluate (id=12) (session_id=EB2788A58B4D0F624CE3D701554EA77B) 2E347B48361BBEE07054DF64D237A35E {
   "result": {
      "description": "1",
      "type": "number",
      "value": 1
   }
}

Jibri 2025-07-19 12:05:59.171 INFO: [63] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-19T12:05:58+0000] [INFO] Waiting for pending navigations...

Jibri 2025-07-19 12:05:59.171 INFO: [63] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-19T12:05:58+0000] [FINE] DevTools WebSocket Command: Runtime.evaluate (id=13) (session_id=EB2788A58B4D0F624CE3D701554EA77B) 2E347B48361BBEE07054DF64D237A35E {
   "expression": "1"
}

Jibri 2025-07-19 12:05:59.172 INFO: [63] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-19T12:05:58+0000] [FINE] DevTools WebSocket Response: Runtime.evaluate (id=13) (session_id=EB2788A58B4D0F624CE3D701554EA77B) 2E347B48361BBEE07054DF64D237A35E {
   "result": {
      "description": "1",
      "type": "number",
      "value": 1
   }
}

Jibri 2025-07-19 12:05:59.172 INFO: [63] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-19T12:05:59+0000] [FINE] DevTools WebSocket Event: Page.domContentEventFired (session_id=EB2788A58B4D0F624CE3D701554EA77B) 2E347B48361BBEE07054DF64D237A35E {
   "timestamp": 642.368563
}

Jibri 2025-07-19 12:05:59.172 INFO: [63] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-19T12:05:59+0000] [FINE] DevTools WebSocket Command: Runtime.evaluate (id=14) (session_id=EB2788A58B4D0F624CE3D701554EA77B) 2E347B48361BBEE07054DF64D237A35E {
   "expression": "1"
}

Jibri 2025-07-19 12:05:59.172 INFO: [63] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-19T12:05:59+0000] [FINE] DevTools WebSocket Event: Page.loadEventFired (session_id=EB2788A58B4D0F624CE3D701554EA77B) 2E347B48361BBEE07054DF64D237A35E {
   "timestamp": 642.391856
}

Jibri 2025-07-19 12:05:59.172 INFO: [63] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-19T12:05:59+0000] [FINE] DevTools WebSocket Command: Runtime.evaluate (id=15) (session_id=EB2788A58B4D0F624CE3D701554EA77B) 2E347B48361BBEE07054DF64D237A35E {
   "awaitPromise": false,
   "expression": "document.readyState",
   "returnByValue": true,
   "uniqueContextId": "-5446052259107647406.-6584951771831250357"
}

Jibri 2025-07-19 12:05:59.173 INFO: [63] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-19T12:05:59+0000] [FINE] DevTools WebSocket Event: Page.frameStoppedLoading (session_id=EB2788A58B4D0F624CE3D701554EA77B) 2E347B48361BBEE07054DF64D237A35E {
   "frameId": "2E347B48361BBEE07054DF64D237A35E"
}

Jibri 2025-07-19 12:05:59.173 INFO: [63] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-19T12:05:59+0000] [FINE] DevTools WebSocket Response: Runtime.evaluate (id=14) (session_id=EB2788A58B4D0F624CE3D701554EA77B) 2E347B48361BBEE07054DF64D237A35E {
   "result": {
      "description": "1",
      "type": "number",
      "value": 1
   }
}

Jibri 2025-07-19 12:05:59.173 INFO: [63] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-19T12:05:59+0000] [FINE] DevTools WebSocket Response: Runtime.evaluate (id=15) (session_id=EB2788A58B4D0F624CE3D701554EA77B) 2E347B48361BBEE07054DF64D237A35E {
   "result": {
      "type": "string",
      "value": "complete"
   }
}

Jibri 2025-07-19 12:05:59.173 INFO: [63] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-19T12:05:59+0000] [INFO] Done waiting for pending navigations. Status: ok

Jibri 2025-07-19 12:05:59.174 INFO: [63] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-19T12:05:59+0000] [FINE] DevTools WebSocket Command: Page.navigate (id=16) (session_id=EB2788A58B4D0F624CE3D701554EA77B) 2E347B48361BBEE07054DF64D237A35E {
   "url": "https://localhost:8443"
}

Jibri 2025-07-19 12:05:59.174 INFO: [63] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-19T12:05:59+0000] [FINE] DevTools WebSocket Event: Page.frameStartedLoading (session_id=EB2788A58B4D0F624CE3D701554EA77B) 2E347B48361BBEE07054DF64D237A35E {
   "frameId": "2E347B48361BBEE07054DF64D237A35E"
}

Jibri 2025-07-19 12:05:59.175 INFO: [63] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-19T12:05:59+0000] [FINE] DevTools WebSocket Response: Page.navigate (id=16) (session_id=EB2788A58B4D0F624CE3D701554EA77B) 2E347B48361BBEE07054DF64D237A35E {
   "errorText": "net::ERR_CONNECTION_REFUSED",
   "frameId": "2E347B48361BBEE07054DF64D237A35E",
   "loaderId": "9BDFA767E726BCFBEAB9136C41FB74D6"
}

Jibri 2025-07-19 12:05:59.175 INFO: [63] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-19T12:05:59+0000] [FINE] DevTools WebSocket Command: Runtime.evaluate (id=17) (session_id=EB2788A58B4D0F624CE3D701554EA77B) 2E347B48361BBEE07054DF64D237A35E {
   "expression": "1"
}

Jibri 2025-07-19 12:05:59.176 INFO: [63] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-19T12:05:59+0000] [FINE] DevTools WebSocket Event: Runtime.executionContextsCleared (session_id=EB2788A58B4D0F624CE3D701554EA77B) 2E347B48361BBEE07054DF64D237A35E {
}

Jibri 2025-07-19 12:05:59.176 INFO: [63] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-19T12:05:59+0000] [FINE] DevTools WebSocket Event: Page.frameNavigated (session_id=EB2788A58B4D0F624CE3D701554EA77B) 2E347B48361BBEE07054DF64D237A35E {
   "frame": {
      "adFrameStatus": {
         "adFrameType": "none"
      },
      "crossOriginIsolatedContextType": "NotIsolated",
      "domainAndRegistry": "",
      "gatedAPIFeatures": [  ],
      "id": "2E347B48361BBEE07054DF64D237A35E",
      "loaderId": "AEEFC0831C3BFF4A0D3740C92662952F",
      "mimeType": "text/html",
      "secureContextType": "InsecureScheme",
      "securityOrigin": "://",
      "unreachableUrl": "https://localhost:8443/",
      "url": "chrome-error://chromewebdata/"
   },
   "type": "Navigation"
}

Jibri 2025-07-19 12:05:59.176 INFO: [63] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-19T12:05:59+0000] [FINE] DevTools WebSocket Event: Runtime.executionContextCreated (session_id=EB2788A58B4D0F624CE3D701554EA77B) 2E347B48361BBEE07054DF64D237A35E {
   "context": {
      "auxData": {
         "frameId": "2E347B48361BBEE07054DF64D237A35E",
         "isDefault": true,
         "type": "default"
      },
      "id": 2,
      "name": "",
      "origin": "://",
      "uniqueId": "-1148956995630653584.984104472335652157"
   }
}

Jibri 2025-07-19 12:05:59.176 INFO: [63] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-19T12:05:59+0000] [FINE] DevTools WebSocket Response: Runtime.evaluate (id=17) (session_id=EB2788A58B4D0F624CE3D701554EA77B) 2E347B48361BBEE07054DF64D237A35E {
   "result": {
      "description": "1",
      "type": "number",
      "value": 1
   }
}

Jibri 2025-07-19 12:05:59.177 INFO: [63] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-19T12:05:59+0000] [INFO] Waiting for pending navigations...

Jibri 2025-07-19 12:05:59.177 INFO: [63] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-19T12:05:59+0000] [FINE] DevTools WebSocket Command: Runtime.evaluate (id=18) (session_id=EB2788A58B4D0F624CE3D701554EA77B) 2E347B48361BBEE07054DF64D237A35E {
   "expression": "1"
}

Jibri 2025-07-19 12:05:59.177 INFO: [63] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-19T12:05:59+0000] [FINE] DevTools WebSocket Event: Page.domContentEventFired (session_id=EB2788A58B4D0F624CE3D701554EA77B) 2E347B48361BBEE07054DF64D237A35E {
   "timestamp": 642.428763
}

Jibri 2025-07-19 12:05:59.177 INFO: [63] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-19T12:05:59+0000] [FINE] DevTools WebSocket Response: Runtime.evaluate (id=18) (session_id=EB2788A58B4D0F624CE3D701554EA77B) 2E347B48361BBEE07054DF64D237A35E {
   "result": {
      "description": "1",
      "type": "number",
      "value": 1
   }
}

Jibri 2025-07-19 12:05:59.177 INFO: [63] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-19T12:05:59+0000] [FINE] DevTools WebSocket Event: Page.loadEventFired (session_id=EB2788A58B4D0F624CE3D701554EA77B) 2E347B48361BBEE07054DF64D237A35E {
   "timestamp": 642.429932
}

Jibri 2025-07-19 12:05:59.178 INFO: [63] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-19T12:05:59+0000] [FINE] DevTools WebSocket Command: Runtime.evaluate (id=19) (session_id=EB2788A58B4D0F624CE3D701554EA77B) 2E347B48361BBEE07054DF64D237A35E {
   "awaitPromise": false,
   "expression": "document.readyState",
   "returnByValue": true,
   "uniqueContextId": "-1148956995630653584.984104472335652157"
}

Jibri 2025-07-19 12:05:59.178 INFO: [63] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-19T12:05:59+0000] [FINE] DevTools WebSocket Event: Page.frameStoppedLoading (session_id=EB2788A58B4D0F624CE3D701554EA77B) 2E347B48361BBEE07054DF64D237A35E {
   "frameId": "2E347B48361BBEE07054DF64D237A35E"
}

Jibri 2025-07-19 12:05:59.178 INFO: [63] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-19T12:05:59+0000] [FINE] DevTools WebSocket Response: Runtime.evaluate (id=19) (session_id=EB2788A58B4D0F624CE3D701554EA77B) 2E347B48361BBEE07054DF64D237A35E {
   "result": {
      "type": "string",
      "value": "complete"
   }
}

Jibri 2025-07-19 12:05:59.179 INFO: [63] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-19T12:05:59+0000] [FINE] DevTools WebSocket Command: Runtime.evaluate (id=20) (session_id=EB2788A58B4D0F624CE3D701554EA77B) 2E347B48361BBEE07054DF64D237A35E {
   "expression": "1"
}

Jibri 2025-07-19 12:05:59.179 INFO: [63] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-19T12:05:59+0000] [FINE] DevTools WebSocket Response: Runtime.evaluate (id=20) (session_id=EB2788A58B4D0F624CE3D701554EA77B) 2E347B48361BBEE07054DF64D237A35E {
   "result": {
      "description": "1",
      "type": "number",
      "value": 1
   }
}

Jibri 2025-07-19 12:05:59.179 INFO: [63] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-19T12:05:59+0000] [INFO] Done waiting for pending navigations. Status: ok

Jibri 2025-07-19 12:05:59.179 INFO: [63] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-19T12:05:59+0000] [FINE] DevTools WebSocket Command: Page.navigate (id=21) (session_id=EB2788A58B4D0F624CE3D701554EA77B) 2E347B48361BBEE07054DF64D237A35E {
   "url": "https://localhost:8443"
}

Jibri 2025-07-19 12:05:59.180 INFO: [63] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-19T12:05:59+0000] [FINE] DevTools WebSocket Event: Page.frameStartedLoading (session_id=EB2788A58B4D0F624CE3D701554EA77B) 2E347B48361BBEE07054DF64D237A35E {
   "frameId": "2E347B48361BBEE07054DF64D237A35E"
}

Jibri 2025-07-19 12:05:59.180 INFO: [63] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-19T12:05:59+0000] [FINE] DevTools WebSocket Response: Page.navigate (id=21) (session_id=EB2788A58B4D0F624CE3D701554EA77B) 2E347B48361BBEE07054DF64D237A35E {
   "errorText": "net::ERR_CONNECTION_REFUSED",
   "frameId": "2E347B48361BBEE07054DF64D237A35E",
   "loaderId": "31646D0AA3681BD0FB724994261E9964"
}

Jibri 2025-07-19 12:05:59.180 INFO: [63] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-19T12:05:59+0000] [FINE] DevTools WebSocket Command: Runtime.evaluate (id=22) (session_id=EB2788A58B4D0F624CE3D701554EA77B) 2E347B48361BBEE07054DF64D237A35E {
   "expression": "1"
}

Jibri 2025-07-19 12:05:59.180 INFO: [63] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-19T12:05:59+0000] [FINE] DevTools WebSocket Event: Runtime.executionContextsCleared (session_id=EB2788A58B4D0F624CE3D701554EA77B) 2E347B48361BBEE07054DF64D237A35E {
}

Jibri 2025-07-19 12:05:59.180 INFO: [63] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-19T12:05:59+0000] [FINE] DevTools WebSocket Event: Page.frameNavigated (session_id=EB2788A58B4D0F624CE3D701554EA77B) 2E347B48361BBEE07054DF64D237A35E {
   "frame": {
      "adFrameStatus": {
         "adFrameType": "none"
      },
      "crossOriginIsolatedContextType": "NotIsolated",
      "domainAndRegistry": "",
      "gatedAPIFeatures": [  ],
      "id": "2E347B48361BBEE07054DF64D237A35E",
      "loaderId": "43A3E825AB606F3DB8F0A1680FB286F4",
      "mimeType": "text/html",
      "secureContextType": "InsecureScheme",
      "securityOrigin": "://",
      "unreachableUrl": "https://localhost:8443/",
      "url": "chrome-error://chromewebdata/"
   },
   "type": "Navigation"
}

Jibri 2025-07-19 12:05:59.181 INFO: [63] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-19T12:05:59+0000] [FINE] DevTools WebSocket Event: Runtime.executionContextCreated (session_id=EB2788A58B4D0F624CE3D701554EA77B) 2E347B48361BBEE07054DF64D237A35E {
   "context": {
      "auxData": {
         "frameId": "2E347B48361BBEE07054DF64D237A35E",
         "isDefault": true,
         "type": "default"
      },
      "id": 3,
      "name": "",
      "origin": "://",
      "uniqueId": "1323491808749340619.1507722924749944852"
   }
}

Jibri 2025-07-19 12:05:59.181 INFO: [63] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-19T12:05:59+0000] [FINE] DevTools WebSocket Response: Runtime.evaluate (id=22) (session_id=EB2788A58B4D0F624CE3D701554EA77B) 2E347B48361BBEE07054DF64D237A35E {
   "result": {
      "description": "1",
      "type": "number",
      "value": 1
   }
}

Jibri 2025-07-19 12:05:59.181 INFO: [63] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-19T12:05:59+0000] [INFO] Waiting for pending navigations...

Jibri 2025-07-19 12:05:59.181 INFO: [63] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-19T12:05:59+0000] [FINE] DevTools WebSocket Command: Runtime.evaluate (id=23) (session_id=EB2788A58B4D0F624CE3D701554EA77B) 2E347B48361BBEE07054DF64D237A35E {
   "expression": "1"
}

Jibri 2025-07-19 12:05:59.181 INFO: [63] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-19T12:05:59+0000] [FINE] DevTools WebSocket Event: Page.domContentEventFired (session_id=EB2788A58B4D0F624CE3D701554EA77B) 2E347B48361BBEE07054DF64D237A35E {
   "timestamp": 642.458322
}

Jibri 2025-07-19 12:05:59.182 INFO: [63] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-19T12:05:59+0000] [FINE] DevTools WebSocket Response: Runtime.evaluate (id=23) (session_id=EB2788A58B4D0F624CE3D701554EA77B) 2E347B48361BBEE07054DF64D237A35E {
   "result": {
      "description": "1",
      "type": "number",
      "value": 1
   }
}

Jibri 2025-07-19 12:05:59.182 INFO: [63] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-19T12:05:59+0000] [FINE] DevTools WebSocket Event: Page.loadEventFired (session_id=EB2788A58B4D0F624CE3D701554EA77B) 2E347B48361BBEE07054DF64D237A35E {
   "timestamp": 642.459712
}

Jibri 2025-07-19 12:05:59.182 INFO: [63] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-19T12:05:59+0000] [FINE] DevTools WebSocket Command: Runtime.evaluate (id=24) (session_id=EB2788A58B4D0F624CE3D701554EA77B) 2E347B48361BBEE07054DF64D237A35E {
   "awaitPromise": false,
   "expression": "document.readyState",
   "returnByValue": true,
   "uniqueContextId": "1323491808749340619.1507722924749944852"
}

Jibri 2025-07-19 12:05:59.182 INFO: [63] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-19T12:05:59+0000] [FINE] DevTools WebSocket Event: Page.frameStoppedLoading (session_id=EB2788A58B4D0F624CE3D701554EA77B) 2E347B48361BBEE07054DF64D237A35E {
   "frameId": "2E347B48361BBEE07054DF64D237A35E"
}

Jibri 2025-07-19 12:05:59.183 INFO: [63] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-19T12:05:59+0000] [FINE] DevTools WebSocket Response: Runtime.evaluate (id=24) (session_id=EB2788A58B4D0F624CE3D701554EA77B) 2E347B48361BBEE07054DF64D237A35E {
   "result": {
      "type": "string",
      "value": "complete"
   }
}

Jibri 2025-07-19 12:05:59.183 INFO: [63] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-19T12:05:59+0000] [FINE] DevTools WebSocket Command: Runtime.evaluate (id=25) (session_id=EB2788A58B4D0F624CE3D701554EA77B) 2E347B48361BBEE07054DF64D237A35E {
   "expression": "1"
}

Jibri 2025-07-19 12:05:59.183 INFO: [63] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-19T12:05:59+0000] [FINE] DevTools WebSocket Response: Runtime.evaluate (id=25) (session_id=EB2788A58B4D0F624CE3D701554EA77B) 2E347B48361BBEE07054DF64D237A35E {
   "result": {
      "description": "1",
      "type": "number",
      "value": 1
   }
}

Jibri 2025-07-19 12:05:59.184 INFO: [63] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-19T12:05:59+0000] [INFO] Done waiting for pending navigations. Status: ok

Jibri 2025-07-19 12:05:59.184 INFO: [63] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-19T12:05:59+0000] [INFO] [c6fb51a812c0875958b82105a25392bd] RESPONSE Navigate ERROR unknown error: net::ERR_CONNECTION_REFUSED
  (Session info: chrome=130.0.6723.116)

Jibri 2025-07-19 12:05:59.184 INFO: [63] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-19T12:05:59+0000] [INFO] [c6fb51a812c0875958b82105a25392bd] COMMAND GetLogTypes {
}

Jibri 2025-07-19 12:05:59.185 INFO: [63] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-19T12:05:59+0000] [INFO] [c6fb51a812c0875958b82105a25392bd] RESPONSE GetLogTypes [ "browser", "driver" ]

Jibri 2025-07-19 12:05:59.185 INFO: [63] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-19T12:05:59+0000] [INFO] [c6fb51a812c0875958b82105a25392bd] COMMAND GetLog {
   "type": "browser"
}

Jibri 2025-07-19 12:05:59.185 INFO: [63] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-19T12:05:59+0000] [FINE] DevTools WebSocket Command: Runtime.evaluate (id=26) (session_id=EB2788A58B4D0F624CE3D701554EA77B) 2E347B48361BBEE07054DF64D237A35E {
   "awaitPromise": false,
   "expression": "1",
   "returnByValue": true
}

Jibri 2025-07-19 12:05:59.185 INFO: [63] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-19T12:05:59+0000] [FINE] DevTools WebSocket Response: Runtime.evaluate (id=26) (session_id=EB2788A58B4D0F624CE3D701554EA77B) 2E347B48361BBEE07054DF64D237A35E {
   "result": {
      "description": "1",
      "type": "number",
      "value": 1
   }
}

Jibri 2025-07-19 12:05:59.185 INFO: [63] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-19T12:05:59+0000] [INFO] [c6fb51a812c0875958b82105a25392bd] RESPONSE GetLog [  ]

Jibri 2025-07-19 12:05:59.185 INFO: [63] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-19T12:05:59+0000] [INFO] [c6fb51a812c0875958b82105a25392bd] COMMAND GetLog {
   "type": "driver"
}

Jibri 2025-07-19 12:05:59.186 INFO: [63] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-19T12:05:59+0000] [FINE] DevTools WebSocket Command: Runtime.evaluate (id=27) (session_id=EB2788A58B4D0F624CE3D701554EA77B) 2E347B48361BBEE07054DF64D237A35E {
   "awaitPromise": false,
   "expression": "1",
   "returnByValue": true
}

Jibri 2025-07-19 12:05:59.186 INFO: [63] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-19T12:05:59+0000] [FINE] DevTools WebSocket Response: Runtime.evaluate (id=27) (session_id=EB2788A58B4D0F624CE3D701554EA77B) 2E347B48361BBEE07054DF64D237A35E {
   "result": {
      "description": "1",
      "type": "number",
      "value": 1
   }
}

Jibri 2025-07-19 12:05:59.186 INFO: [63] JibriSelenium.leaveCallAndQuitBrowser#354: ========= TYPE=client ===========
