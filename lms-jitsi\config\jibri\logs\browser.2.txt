Jibri 2025-07-08 12:41:12.818 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#349: Logs for call null
Jibri 2025-07-08 12:41:12.832 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#354: ========= TYPE=browser ===========
Jibri 2025-07-08 12:41:12.849 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#354: ========= TYPE=driver ===========
Jibri 2025-07-08 12:41:12.849 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T12:41:12+0000] [INFO] Browser search. Trying... /usr/bin/chrome

Jibri 2025-07-08 12:41:12.850 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T12:41:12+0000] [INFO] Browser search. Trying... /usr/bin/chrome

Jibri 2025-07-08 12:41:12.850 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T12:41:12+0000] [INFO] Browser search. Trying... /usr/bin/google-chrome

Jibri 2025-07-08 12:41:12.850 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T12:41:12+0000] [INFO] Browser search. Found at  /usr/bin/google-chrome

Jibri 2025-07-08 12:41:12.850 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T12:41:12+0000] [INFO] Populating Preferences file: {
   "alternate_error_pages": {
      "enabled": false
   },
   "autofill": {
      "enabled": false
   },
   "browser": {
      "check_default_browser": false
   },
   "distribution": {
      "import_bookmarks": false,
      "import_history": false,
      "import_search_engine": false,
      "make_chrome_default_for_user": false,
      "skip_first_run_ui": true
   },
   "dns_prefetching": {
      "enabled": false
   },
   "profile": {
      "content_settings": {
         "pattern_pairs": {
            "https://*,*": {
               "media-stream": {
                  "audio": "Default",
                  "video": "Default"
               }
            }
         }
      },
      "default_content_setting_values": {
         "geolocation": 1
      },
      "default_content_settings": {
         "geolocation": 1,
         "mouselock": 1,
         "notifications": 1,
         "popups": 1,
         "ppapi-broker": 1
      },
      "password_manager_enabled": false
   },
   "safebrowsing": {
      "enabled": false
   },
   "search": {
      "suggest_enabled": false
   },
   "translate": {
      "enabled": false
   }
}

Jibri 2025-07-08 12:41:12.851 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T12:41:12+0000] [INFO] Populating Local State file: {
   "background_mode": {
      "enabled": false
   },
   "ssl": {
      "rev_checking": {
         "enabled": false
      }
   }
}

Jibri 2025-07-08 12:41:12.851 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T12:41:12+0000] [INFO] ChromeDriver supports communication with Chrome via pipes. This is more reliable and more secure.

Jibri 2025-07-08 12:41:12.851 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T12:41:12+0000] [INFO] Use the --remote-debugging-pipe Chrome switch instead of the default --remote-debugging-port to enable this communication mode.

Jibri 2025-07-08 12:41:12.851 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T12:41:12+0000] [INFO] Launching chrome: /usr/bin/google-chrome --allow-pre-commit-input --autoplay-policy=no-user-gesture-required --disable-background-networking --disable-client-side-phishing-detection --disable-default-apps --disable-hang-monitor --disable-popup-blocking --disable-prompt-on-repost --disable-sync --enable-automation --enable-logging --enabled --kiosk --log-level=0 --no-first-run --no-service-autorun --password-store=basic --remote-debugging-port=0 --start-maximized --test-type=webdriver --use-fake-ui-for-media-stream --use-mock-keychain --user-data-dir=/tmp/.org.chromium.Chromium.P7gTbf data:,

Jibri 2025-07-08 12:41:12.852 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T12:41:12+0000] [FINE] DevTools HTTP Request: http://localhost:43557/json/version

Jibri 2025-07-08 12:41:12.852 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T12:41:12+0000] [FINE] DevTools HTTP Response: {
   "Browser": "Chrome/130.0.6723.116",
   "Protocol-Version": "1.3",
   "User-Agent": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36",
   "V8-Version": "***********",
   "WebKit-Version": "537.36 (@6ac35f94ae3d01152cf1946c896b0678e48f8ec4)",
   "webSocketDebuggerUrl": "ws://localhost:43557/devtools/browser/7810f47b-043e-436c-9411-a4ab1bed7ed1"
}


Jibri 2025-07-08 12:41:12.852 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T12:41:12+0000] [FINE] DevTools HTTP Request: http://localhost:43557/json/list

Jibri 2025-07-08 12:41:12.853 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T12:41:12+0000] [FINE] DevTools HTTP Response: [ {
   "description": "",
   "devtoolsFrontendUrl": "/devtools/inspector.html?ws=localhost:43557/devtools/page/168CC86AE53E1EF23996F16DA2F40D4E",
   "id": "168CC86AE53E1EF23996F16DA2F40D4E",
   "title": "",
   "type": "page",
   "url": "data:,",
   "webSocketDebuggerUrl": "ws://localhost:43557/devtools/page/168CC86AE53E1EF23996F16DA2F40D4E"
} ]


Jibri 2025-07-08 12:41:12.853 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T12:41:12+0000] [FINE] DevTools WebSocket Command: Target.getTargets (id=1) (session_id=) browser {
}

Jibri 2025-07-08 12:41:12.853 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T12:41:12+0000] [FINE] DevTools WebSocket Response: Target.getTargets (id=1) (session_id=) browser {
   "targetInfos": [ {
      "attached": false,
      "browserContextId": "DE69590BE9F5A5ABCAD66509AD6863C1",
      "canAccessOpener": false,
      "targetId": "168CC86AE53E1EF23996F16DA2F40D4E",
      "title": "",
      "type": "page",
      "url": "data:,"
   } ]
}

Jibri 2025-07-08 12:41:12.854 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T12:41:12+0000] [FINE] DevTools WebSocket Command: Target.attachToTarget (id=2) (session_id=) browser {
   "flatten": true,
   "targetId": "168CC86AE53E1EF23996F16DA2F40D4E"
}

Jibri 2025-07-08 12:41:12.854 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T12:41:12+0000] [FINE] DevTools WebSocket Event: Target.attachedToTarget (session_id=) browser {
   "sessionId": "999625059EF0CFFCD977585239D7DA4A",
   "targetInfo": {
      "attached": true,
      "browserContextId": "DE69590BE9F5A5ABCAD66509AD6863C1",
      "canAccessOpener": false,
      "targetId": "168CC86AE53E1EF23996F16DA2F40D4E",
      "title": "",
      "type": "page",
      "url": "data:,"
   },
   "waitingForDebugger": false
}

Jibri 2025-07-08 12:41:12.854 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T12:41:12+0000] [FINE] DevTools WebSocket Response: Target.attachToTarget (id=2) (session_id=) browser {
   "sessionId": "999625059EF0CFFCD977585239D7DA4A"
}

Jibri 2025-07-08 12:41:12.855 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T12:41:12+0000] [FINE] DevTools WebSocket Command: Page.enable (id=3) (session_id=999625059EF0CFFCD977585239D7DA4A) 168CC86AE53E1EF23996F16DA2F40D4E {
}

Jibri 2025-07-08 12:41:12.855 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T12:41:12+0000] [FINE] DevTools WebSocket Command: Page.addScriptToEvaluateOnNewDocument (id=4) (session_id=999625059EF0CFFCD977585239D7DA4A) 168CC86AE53E1EF23996F16DA2F40D4E {
   "source": "(function () {window.cdc_adoQpoasnfa76pfcZLmcfl_Array = window.Array;window.cdc_adoQpoasnfa76pfcZLmcfl_Object = window.Object;window.cdc_adoQpoasnfa76pfcZLmcfl_Promise = window.Promise;window.cdc_a..."
}

Jibri 2025-07-08 12:41:12.855 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T12:41:12+0000] [FINE] DevTools WebSocket Command: Runtime.evaluate (id=5) (session_id=999625059EF0CFFCD977585239D7DA4A) 168CC86AE53E1EF23996F16DA2F40D4E {
   "expression": "(function () {window.cdc_adoQpoasnfa76pfcZLmcfl_Array = window.Array;window.cdc_adoQpoasnfa76pfcZLmcfl_Object = window.Object;window.cdc_adoQpoasnfa76pfcZLmcfl_Promise = window.Promise;window.cdc_a..."
}

Jibri 2025-07-08 12:41:12.855 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T12:41:12+0000] [FINE] DevTools WebSocket Command: Log.enable (id=6) (session_id=999625059EF0CFFCD977585239D7DA4A) 168CC86AE53E1EF23996F16DA2F40D4E {
}

Jibri 2025-07-08 12:41:12.856 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T12:41:12+0000] [FINE] DevTools WebSocket Command: Target.setAutoAttach (id=7) (session_id=999625059EF0CFFCD977585239D7DA4A) 168CC86AE53E1EF23996F16DA2F40D4E {
   "autoAttach": true,
   "flatten": true,
   "waitForDebuggerOnStart": false
}

Jibri 2025-07-08 12:41:12.856 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T12:41:12+0000] [FINE] DevTools WebSocket Response: Page.enable (id=3) (session_id=999625059EF0CFFCD977585239D7DA4A) 168CC86AE53E1EF23996F16DA2F40D4E {
}

Jibri 2025-07-08 12:41:12.856 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T12:41:12+0000] [FINE] DevTools WebSocket Response: Page.addScriptToEvaluateOnNewDocument (id=4) (session_id=999625059EF0CFFCD977585239D7DA4A) 168CC86AE53E1EF23996F16DA2F40D4E {
   "identifier": "1"
}

Jibri 2025-07-08 12:41:12.856 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T12:41:12+0000] [FINE] DevTools WebSocket Response: Runtime.evaluate (id=5) (session_id=999625059EF0CFFCD977585239D7DA4A) 168CC86AE53E1EF23996F16DA2F40D4E {
   "result": {
      "type": "undefined"
   }
}

Jibri 2025-07-08 12:41:12.857 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T12:41:12+0000] [FINE] DevTools WebSocket Response: Log.enable (id=6) (session_id=999625059EF0CFFCD977585239D7DA4A) 168CC86AE53E1EF23996F16DA2F40D4E {
}

Jibri 2025-07-08 12:41:12.857 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T12:41:12+0000] [FINE] DevTools WebSocket Response: Target.setAutoAttach (id=7) (session_id=999625059EF0CFFCD977585239D7DA4A) 168CC86AE53E1EF23996F16DA2F40D4E {
}

Jibri 2025-07-08 12:41:12.857 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T12:41:12+0000] [FINE] DevTools WebSocket Command: Runtime.enable (id=8) (session_id=999625059EF0CFFCD977585239D7DA4A) 168CC86AE53E1EF23996F16DA2F40D4E {
}

Jibri 2025-07-08 12:41:12.857 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T12:41:12+0000] [FINE] DevTools WebSocket Event: Page.domContentEventFired (session_id=999625059EF0CFFCD977585239D7DA4A) 168CC86AE53E1EF23996F16DA2F40D4E {
   "timestamp": 9619.609027
}

Jibri 2025-07-08 12:41:12.858 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T12:41:12+0000] [FINE] DevTools WebSocket Event: Page.loadEventFired (session_id=999625059EF0CFFCD977585239D7DA4A) 168CC86AE53E1EF23996F16DA2F40D4E {
   "timestamp": 9619.609501
}

Jibri 2025-07-08 12:41:12.858 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T12:41:12+0000] [FINE] DevTools WebSocket Event: Page.frameStoppedLoading (session_id=999625059EF0CFFCD977585239D7DA4A) 168CC86AE53E1EF23996F16DA2F40D4E {
   "frameId": "168CC86AE53E1EF23996F16DA2F40D4E"
}

Jibri 2025-07-08 12:41:12.858 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T12:41:12+0000] [FINE] DevTools WebSocket Event: Page.frameResized (session_id=999625059EF0CFFCD977585239D7DA4A) 168CC86AE53E1EF23996F16DA2F40D4E {
}

Jibri 2025-07-08 12:41:12.858 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T12:41:12+0000] [FINE] DevTools WebSocket Event: Runtime.executionContextCreated (session_id=999625059EF0CFFCD977585239D7DA4A) 168CC86AE53E1EF23996F16DA2F40D4E {
   "context": {
      "auxData": {
         "frameId": "168CC86AE53E1EF23996F16DA2F40D4E",
         "isDefault": true,
         "type": "default"
      },
      "id": 1,
      "name": "",
      "origin": "://",
      "uniqueId": "-7643381482788603628.-1420881460199041056"
   }
}

Jibri 2025-07-08 12:41:12.859 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T12:41:12+0000] [FINE] DevTools WebSocket Response: Runtime.enable (id=8) (session_id=999625059EF0CFFCD977585239D7DA4A) 168CC86AE53E1EF23996F16DA2F40D4E {
}

Jibri 2025-07-08 12:41:12.859 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T12:41:12+0000] [FINE] DevTools WebSocket Command: Runtime.enable (id=9) (session_id=999625059EF0CFFCD977585239D7DA4A) 168CC86AE53E1EF23996F16DA2F40D4E {
}

Jibri 2025-07-08 12:41:12.859 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T12:41:12+0000] [FINE] DevTools WebSocket Response: Runtime.enable (id=9) (session_id=999625059EF0CFFCD977585239D7DA4A) 168CC86AE53E1EF23996F16DA2F40D4E {
}

Jibri 2025-07-08 12:41:12.859 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T12:41:12+0000] [INFO] [f542fb5ab80b3baad445754e5e802ece] RESPONSE InitSession {
   "acceptInsecureCerts": false,
   "acceptSslCerts": false,
   "browserConnectionEnabled": false,
   "browserName": "chrome",
   "chrome": {
      "chromedriverVersion": "130.0.6723.116 (6ac35f94ae3d01152cf1946c896b0678e48f8ec4-refs/branch-heads/6723@{#1764})",
      "userDataDir": "/tmp/.org.chromium.Chromium.P7gTbf"
   },
   "cssSelectorsEnabled": true,
   "databaseEnabled": false,
   "fedcm:accounts": true,
   "goog:chromeOptions": {
      "debuggerAddress": "localhost:43557"
   },
   "handlesAlerts": true,
   "hasTouchScreen": false,
   "javascriptEnabled": true,
   "locationContextEnabled": true,
   "mobileEmulationEnabled": false,
   "nativeEvents": true,
   "networkConnectionEnabled": false,
   "pageLoadStrategy": "normal",
   "platform": "Linux",
   "proxy": {
   },
   "~~~": "..."
}

Jibri 2025-07-08 12:41:12.860 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T12:41:12+0000] [INFO] [f542fb5ab80b3baad445754e5e802ece] COMMAND SetTimeouts {
   "ms": 60000,
   "type": "page load"
}

Jibri 2025-07-08 12:41:12.860 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T12:41:12+0000] [INFO] [f542fb5ab80b3baad445754e5e802ece] RESPONSE SetTimeouts

Jibri 2025-07-08 12:41:12.860 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T12:41:12+0000] [INFO] [f542fb5ab80b3baad445754e5e802ece] COMMAND Navigate {
   "url": "https://localhost:8443"
}

Jibri 2025-07-08 12:41:12.860 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T12:41:12+0000] [INFO] Waiting for pending navigations...

Jibri 2025-07-08 12:41:12.861 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T12:41:12+0000] [FINE] DevTools WebSocket Command: Runtime.evaluate (id=10) (session_id=999625059EF0CFFCD977585239D7DA4A) 168CC86AE53E1EF23996F16DA2F40D4E {
   "expression": "1"
}

Jibri 2025-07-08 12:41:12.861 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T12:41:12+0000] [FINE] DevTools WebSocket Response: Runtime.evaluate (id=10) (session_id=999625059EF0CFFCD977585239D7DA4A) 168CC86AE53E1EF23996F16DA2F40D4E {
   "result": {
      "description": "1",
      "type": "number",
      "value": 1
   }
}

Jibri 2025-07-08 12:41:12.861 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T12:41:12+0000] [INFO] Done waiting for pending navigations. Status: ok

Jibri 2025-07-08 12:41:12.861 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T12:41:12+0000] [FINE] DevTools WebSocket Command: Page.navigate (id=11) (session_id=999625059EF0CFFCD977585239D7DA4A) 168CC86AE53E1EF23996F16DA2F40D4E {
   "url": "https://localhost:8443"
}

Jibri 2025-07-08 12:41:12.862 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T12:41:12+0000] [FINE] DevTools WebSocket Event: Page.frameStartedLoading (session_id=999625059EF0CFFCD977585239D7DA4A) 168CC86AE53E1EF23996F16DA2F40D4E {
   "frameId": "168CC86AE53E1EF23996F16DA2F40D4E"
}

Jibri 2025-07-08 12:41:12.862 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T12:41:12+0000] [FINE] DevTools WebSocket Response: Page.navigate (id=11) (session_id=999625059EF0CFFCD977585239D7DA4A) 168CC86AE53E1EF23996F16DA2F40D4E {
   "errorText": "net::ERR_CONNECTION_REFUSED",
   "frameId": "168CC86AE53E1EF23996F16DA2F40D4E",
   "loaderId": "59D072E301420E534C5219671D7D85B7"
}

Jibri 2025-07-08 12:41:12.862 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T12:41:12+0000] [FINE] DevTools WebSocket Command: Runtime.evaluate (id=12) (session_id=999625059EF0CFFCD977585239D7DA4A) 168CC86AE53E1EF23996F16DA2F40D4E {
   "expression": "1"
}

Jibri 2025-07-08 12:41:12.863 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T12:41:12+0000] [FINE] DevTools WebSocket Event: Runtime.executionContextsCleared (session_id=999625059EF0CFFCD977585239D7DA4A) 168CC86AE53E1EF23996F16DA2F40D4E {
}

Jibri 2025-07-08 12:41:12.863 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T12:41:12+0000] [FINE] DevTools WebSocket Event: Runtime.executionContextsCleared (session_id=999625059EF0CFFCD977585239D7DA4A) 168CC86AE53E1EF23996F16DA2F40D4E {
}

Jibri 2025-07-08 12:41:12.863 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T12:41:12+0000] [FINE] DevTools WebSocket Event: Page.frameNavigated (session_id=999625059EF0CFFCD977585239D7DA4A) 168CC86AE53E1EF23996F16DA2F40D4E {
   "frame": {
      "adFrameStatus": {
         "adFrameType": "none"
      },
      "crossOriginIsolatedContextType": "NotIsolated",
      "domainAndRegistry": "",
      "gatedAPIFeatures": [  ],
      "id": "168CC86AE53E1EF23996F16DA2F40D4E",
      "loaderId": "395A6EBC312017C94B0F9AAFB21A3E85",
      "mimeType": "text/html",
      "secureContextType": "InsecureScheme",
      "securityOrigin": "://",
      "unreachableUrl": "https://localhost:8443/",
      "url": "chrome-error://chromewebdata/"
   },
   "type": "Navigation"
}

Jibri 2025-07-08 12:41:12.864 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T12:41:12+0000] [FINE] DevTools WebSocket Event: Runtime.executionContextCreated (session_id=999625059EF0CFFCD977585239D7DA4A) 168CC86AE53E1EF23996F16DA2F40D4E {
   "context": {
      "auxData": {
         "frameId": "168CC86AE53E1EF23996F16DA2F40D4E",
         "isDefault": true,
         "type": "default"
      },
      "id": 1,
      "name": "",
      "origin": "://",
      "uniqueId": "6955616971450549534.-6477871691473236656"
   }
}

Jibri 2025-07-08 12:41:12.864 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T12:41:12+0000] [FINE] DevTools WebSocket Response: Runtime.evaluate (id=12) (session_id=999625059EF0CFFCD977585239D7DA4A) 168CC86AE53E1EF23996F16DA2F40D4E {
   "result": {
      "description": "1",
      "type": "number",
      "value": 1
   }
}

Jibri 2025-07-08 12:41:12.864 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T12:41:12+0000] [INFO] Waiting for pending navigations...

Jibri 2025-07-08 12:41:12.864 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T12:41:12+0000] [FINE] DevTools WebSocket Command: Runtime.evaluate (id=13) (session_id=999625059EF0CFFCD977585239D7DA4A) 168CC86AE53E1EF23996F16DA2F40D4E {
   "expression": "1"
}

Jibri 2025-07-08 12:41:12.865 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T12:41:12+0000] [FINE] DevTools WebSocket Event: Page.domContentEventFired (session_id=999625059EF0CFFCD977585239D7DA4A) 168CC86AE53E1EF23996F16DA2F40D4E {
   "timestamp": 9619.719388
}

Jibri 2025-07-08 12:41:12.865 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T12:41:12+0000] [FINE] DevTools WebSocket Response: Runtime.evaluate (id=13) (session_id=999625059EF0CFFCD977585239D7DA4A) 168CC86AE53E1EF23996F16DA2F40D4E {
   "result": {
      "description": "1",
      "type": "number",
      "value": 1
   }
}

Jibri 2025-07-08 12:41:12.865 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T12:41:12+0000] [FINE] DevTools WebSocket Event: Page.loadEventFired (session_id=999625059EF0CFFCD977585239D7DA4A) 168CC86AE53E1EF23996F16DA2F40D4E {
   "timestamp": 9619.721612
}

Jibri 2025-07-08 12:41:12.865 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T12:41:12+0000] [FINE] DevTools WebSocket Command: Runtime.evaluate (id=14) (session_id=999625059EF0CFFCD977585239D7DA4A) 168CC86AE53E1EF23996F16DA2F40D4E {
   "awaitPromise": false,
   "expression": "document.readyState",
   "returnByValue": true,
   "uniqueContextId": "6955616971450549534.-6477871691473236656"
}

Jibri 2025-07-08 12:41:12.866 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T12:41:12+0000] [FINE] DevTools WebSocket Event: Page.frameStoppedLoading (session_id=999625059EF0CFFCD977585239D7DA4A) 168CC86AE53E1EF23996F16DA2F40D4E {
   "frameId": "168CC86AE53E1EF23996F16DA2F40D4E"
}

Jibri 2025-07-08 12:41:12.866 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T12:41:12+0000] [FINE] DevTools WebSocket Response: Runtime.evaluate (id=14) (session_id=999625059EF0CFFCD977585239D7DA4A) 168CC86AE53E1EF23996F16DA2F40D4E {
   "result": {
      "type": "string",
      "value": "complete"
   }
}

Jibri 2025-07-08 12:41:12.866 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T12:41:12+0000] [FINE] DevTools WebSocket Command: Runtime.evaluate (id=15) (session_id=999625059EF0CFFCD977585239D7DA4A) 168CC86AE53E1EF23996F16DA2F40D4E {
   "expression": "1"
}

Jibri 2025-07-08 12:41:12.866 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T12:41:12+0000] [FINE] DevTools WebSocket Response: Runtime.evaluate (id=15) (session_id=999625059EF0CFFCD977585239D7DA4A) 168CC86AE53E1EF23996F16DA2F40D4E {
   "result": {
      "description": "1",
      "type": "number",
      "value": 1
   }
}

Jibri 2025-07-08 12:41:12.867 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T12:41:12+0000] [INFO] Done waiting for pending navigations. Status: ok

Jibri 2025-07-08 12:41:12.867 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T12:41:12+0000] [FINE] DevTools WebSocket Command: Page.navigate (id=16) (session_id=999625059EF0CFFCD977585239D7DA4A) 168CC86AE53E1EF23996F16DA2F40D4E {
   "url": "https://localhost:8443"
}

Jibri 2025-07-08 12:41:12.867 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T12:41:12+0000] [FINE] DevTools WebSocket Event: Page.frameStartedLoading (session_id=999625059EF0CFFCD977585239D7DA4A) 168CC86AE53E1EF23996F16DA2F40D4E {
   "frameId": "168CC86AE53E1EF23996F16DA2F40D4E"
}

Jibri 2025-07-08 12:41:12.867 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T12:41:12+0000] [FINE] DevTools WebSocket Response: Page.navigate (id=16) (session_id=999625059EF0CFFCD977585239D7DA4A) 168CC86AE53E1EF23996F16DA2F40D4E {
   "errorText": "net::ERR_CONNECTION_REFUSED",
   "frameId": "168CC86AE53E1EF23996F16DA2F40D4E",
   "loaderId": "BFAF36C32C28B56ABF65AFF14E2964AD"
}

Jibri 2025-07-08 12:41:12.867 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T12:41:12+0000] [FINE] DevTools WebSocket Command: Runtime.evaluate (id=17) (session_id=999625059EF0CFFCD977585239D7DA4A) 168CC86AE53E1EF23996F16DA2F40D4E {
   "expression": "1"
}

Jibri 2025-07-08 12:41:12.868 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T12:41:12+0000] [FINE] DevTools WebSocket Event: Runtime.executionContextsCleared (session_id=999625059EF0CFFCD977585239D7DA4A) 168CC86AE53E1EF23996F16DA2F40D4E {
}

Jibri 2025-07-08 12:41:12.868 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T12:41:12+0000] [FINE] DevTools WebSocket Event: Page.frameNavigated (session_id=999625059EF0CFFCD977585239D7DA4A) 168CC86AE53E1EF23996F16DA2F40D4E {
   "frame": {
      "adFrameStatus": {
         "adFrameType": "none"
      },
      "crossOriginIsolatedContextType": "NotIsolated",
      "domainAndRegistry": "",
      "gatedAPIFeatures": [  ],
      "id": "168CC86AE53E1EF23996F16DA2F40D4E",
      "loaderId": "DA8FE252AB12E4416317A72387983634",
      "mimeType": "text/html",
      "secureContextType": "InsecureScheme",
      "securityOrigin": "://",
      "unreachableUrl": "https://localhost:8443/",
      "url": "chrome-error://chromewebdata/"
   },
   "type": "Navigation"
}

Jibri 2025-07-08 12:41:12.868 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T12:41:12+0000] [FINE] DevTools WebSocket Event: Runtime.executionContextCreated (session_id=999625059EF0CFFCD977585239D7DA4A) 168CC86AE53E1EF23996F16DA2F40D4E {
   "context": {
      "auxData": {
         "frameId": "168CC86AE53E1EF23996F16DA2F40D4E",
         "isDefault": true,
         "type": "default"
      },
      "id": 2,
      "name": "",
      "origin": "://",
      "uniqueId": "938413743937218168.-813057226463470947"
   }
}

Jibri 2025-07-08 12:41:12.868 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T12:41:12+0000] [FINE] DevTools WebSocket Response: Runtime.evaluate (id=17) (session_id=999625059EF0CFFCD977585239D7DA4A) 168CC86AE53E1EF23996F16DA2F40D4E {
   "result": {
      "description": "1",
      "type": "number",
      "value": 1
   }
}

Jibri 2025-07-08 12:41:12.869 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T12:41:12+0000] [INFO] Waiting for pending navigations...

Jibri 2025-07-08 12:41:12.869 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T12:41:12+0000] [FINE] DevTools WebSocket Command: Runtime.evaluate (id=18) (session_id=999625059EF0CFFCD977585239D7DA4A) 168CC86AE53E1EF23996F16DA2F40D4E {
   "expression": "1"
}

Jibri 2025-07-08 12:41:12.869 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T12:41:12+0000] [FINE] DevTools WebSocket Event: Page.domContentEventFired (session_id=999625059EF0CFFCD977585239D7DA4A) 168CC86AE53E1EF23996F16DA2F40D4E {
   "timestamp": 9619.755108
}

Jibri 2025-07-08 12:41:12.870 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T12:41:12+0000] [FINE] DevTools WebSocket Response: Runtime.evaluate (id=18) (session_id=999625059EF0CFFCD977585239D7DA4A) 168CC86AE53E1EF23996F16DA2F40D4E {
   "result": {
      "description": "1",
      "type": "number",
      "value": 1
   }
}

Jibri 2025-07-08 12:41:12.870 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T12:41:12+0000] [FINE] DevTools WebSocket Event: Page.loadEventFired (session_id=999625059EF0CFFCD977585239D7DA4A) 168CC86AE53E1EF23996F16DA2F40D4E {
   "timestamp": 9619.756245
}

Jibri 2025-07-08 12:41:12.870 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T12:41:12+0000] [FINE] DevTools WebSocket Command: Runtime.evaluate (id=19) (session_id=999625059EF0CFFCD977585239D7DA4A) 168CC86AE53E1EF23996F16DA2F40D4E {
   "awaitPromise": false,
   "expression": "document.readyState",
   "returnByValue": true,
   "uniqueContextId": "938413743937218168.-813057226463470947"
}

Jibri 2025-07-08 12:41:12.871 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T12:41:12+0000] [FINE] DevTools WebSocket Event: Page.frameStoppedLoading (session_id=999625059EF0CFFCD977585239D7DA4A) 168CC86AE53E1EF23996F16DA2F40D4E {
   "frameId": "168CC86AE53E1EF23996F16DA2F40D4E"
}

Jibri 2025-07-08 12:41:12.871 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T12:41:12+0000] [FINE] DevTools WebSocket Response: Runtime.evaluate (id=19) (session_id=999625059EF0CFFCD977585239D7DA4A) 168CC86AE53E1EF23996F16DA2F40D4E {
   "result": {
      "type": "string",
      "value": "complete"
   }
}

Jibri 2025-07-08 12:41:12.871 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T12:41:12+0000] [FINE] DevTools WebSocket Command: Runtime.evaluate (id=20) (session_id=999625059EF0CFFCD977585239D7DA4A) 168CC86AE53E1EF23996F16DA2F40D4E {
   "expression": "1"
}

Jibri 2025-07-08 12:41:12.872 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T12:41:12+0000] [FINE] DevTools WebSocket Response: Runtime.evaluate (id=20) (session_id=999625059EF0CFFCD977585239D7DA4A) 168CC86AE53E1EF23996F16DA2F40D4E {
   "result": {
      "description": "1",
      "type": "number",
      "value": 1
   }
}

Jibri 2025-07-08 12:41:12.872 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T12:41:12+0000] [INFO] Done waiting for pending navigations. Status: ok

Jibri 2025-07-08 12:41:12.872 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T12:41:12+0000] [FINE] DevTools WebSocket Command: Page.navigate (id=21) (session_id=999625059EF0CFFCD977585239D7DA4A) 168CC86AE53E1EF23996F16DA2F40D4E {
   "url": "https://localhost:8443"
}

Jibri 2025-07-08 12:41:12.872 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T12:41:12+0000] [FINE] DevTools WebSocket Event: Page.frameStartedLoading (session_id=999625059EF0CFFCD977585239D7DA4A) 168CC86AE53E1EF23996F16DA2F40D4E {
   "frameId": "168CC86AE53E1EF23996F16DA2F40D4E"
}

Jibri 2025-07-08 12:41:12.873 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T12:41:12+0000] [FINE] DevTools WebSocket Response: Page.navigate (id=21) (session_id=999625059EF0CFFCD977585239D7DA4A) 168CC86AE53E1EF23996F16DA2F40D4E {
   "errorText": "net::ERR_CONNECTION_REFUSED",
   "frameId": "168CC86AE53E1EF23996F16DA2F40D4E",
   "loaderId": "233F552647F9420A7D9E149AF5227CA2"
}

Jibri 2025-07-08 12:41:12.873 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T12:41:12+0000] [FINE] DevTools WebSocket Command: Runtime.evaluate (id=22) (session_id=999625059EF0CFFCD977585239D7DA4A) 168CC86AE53E1EF23996F16DA2F40D4E {
   "expression": "1"
}

Jibri 2025-07-08 12:41:12.873 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T12:41:12+0000] [FINE] DevTools WebSocket Event: Runtime.executionContextsCleared (session_id=999625059EF0CFFCD977585239D7DA4A) 168CC86AE53E1EF23996F16DA2F40D4E {
}

Jibri 2025-07-08 12:41:12.873 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T12:41:12+0000] [FINE] DevTools WebSocket Event: Page.frameNavigated (session_id=999625059EF0CFFCD977585239D7DA4A) 168CC86AE53E1EF23996F16DA2F40D4E {
   "frame": {
      "adFrameStatus": {
         "adFrameType": "none"
      },
      "crossOriginIsolatedContextType": "NotIsolated",
      "domainAndRegistry": "",
      "gatedAPIFeatures": [  ],
      "id": "168CC86AE53E1EF23996F16DA2F40D4E",
      "loaderId": "6FDC4E660BDEB65542E5931B03FAB88F",
      "mimeType": "text/html",
      "secureContextType": "InsecureScheme",
      "securityOrigin": "://",
      "unreachableUrl": "https://localhost:8443/",
      "url": "chrome-error://chromewebdata/"
   },
   "type": "Navigation"
}

Jibri 2025-07-08 12:41:12.873 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T12:41:12+0000] [FINE] DevTools WebSocket Event: Runtime.executionContextCreated (session_id=999625059EF0CFFCD977585239D7DA4A) 168CC86AE53E1EF23996F16DA2F40D4E {
   "context": {
      "auxData": {
         "frameId": "168CC86AE53E1EF23996F16DA2F40D4E",
         "isDefault": true,
         "type": "default"
      },
      "id": 3,
      "name": "",
      "origin": "://",
      "uniqueId": "3420827013635746289.7914815866996881475"
   }
}

Jibri 2025-07-08 12:41:12.874 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T12:41:12+0000] [FINE] DevTools WebSocket Response: Runtime.evaluate (id=22) (session_id=999625059EF0CFFCD977585239D7DA4A) 168CC86AE53E1EF23996F16DA2F40D4E {
   "result": {
      "description": "1",
      "type": "number",
      "value": 1
   }
}

Jibri 2025-07-08 12:41:12.874 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T12:41:12+0000] [INFO] Waiting for pending navigations...

Jibri 2025-07-08 12:41:12.874 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T12:41:12+0000] [FINE] DevTools WebSocket Command: Runtime.evaluate (id=23) (session_id=999625059EF0CFFCD977585239D7DA4A) 168CC86AE53E1EF23996F16DA2F40D4E {
   "expression": "1"
}

Jibri 2025-07-08 12:41:12.874 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T12:41:12+0000] [FINE] DevTools WebSocket Event: Page.domContentEventFired (session_id=999625059EF0CFFCD977585239D7DA4A) 168CC86AE53E1EF23996F16DA2F40D4E {
   "timestamp": 9619.783898
}

Jibri 2025-07-08 12:41:12.875 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T12:41:12+0000] [FINE] DevTools WebSocket Response: Runtime.evaluate (id=23) (session_id=999625059EF0CFFCD977585239D7DA4A) 168CC86AE53E1EF23996F16DA2F40D4E {
   "result": {
      "description": "1",
      "type": "number",
      "value": 1
   }
}

Jibri 2025-07-08 12:41:12.875 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T12:41:12+0000] [FINE] DevTools WebSocket Event: Page.loadEventFired (session_id=999625059EF0CFFCD977585239D7DA4A) 168CC86AE53E1EF23996F16DA2F40D4E {
   "timestamp": 9619.78529
}

Jibri 2025-07-08 12:41:12.875 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T12:41:12+0000] [FINE] DevTools WebSocket Command: Runtime.evaluate (id=24) (session_id=999625059EF0CFFCD977585239D7DA4A) 168CC86AE53E1EF23996F16DA2F40D4E {
   "awaitPromise": false,
   "expression": "document.readyState",
   "returnByValue": true,
   "uniqueContextId": "3420827013635746289.7914815866996881475"
}

Jibri 2025-07-08 12:41:12.875 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T12:41:12+0000] [FINE] DevTools WebSocket Event: Page.frameStoppedLoading (session_id=999625059EF0CFFCD977585239D7DA4A) 168CC86AE53E1EF23996F16DA2F40D4E {
   "frameId": "168CC86AE53E1EF23996F16DA2F40D4E"
}

Jibri 2025-07-08 12:41:12.875 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T12:41:12+0000] [FINE] DevTools WebSocket Response: Runtime.evaluate (id=24) (session_id=999625059EF0CFFCD977585239D7DA4A) 168CC86AE53E1EF23996F16DA2F40D4E {
   "result": {
      "type": "string",
      "value": "complete"
   }
}

Jibri 2025-07-08 12:41:12.876 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T12:41:12+0000] [FINE] DevTools WebSocket Command: Runtime.evaluate (id=25) (session_id=999625059EF0CFFCD977585239D7DA4A) 168CC86AE53E1EF23996F16DA2F40D4E {
   "expression": "1"
}

Jibri 2025-07-08 12:41:12.876 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T12:41:12+0000] [FINE] DevTools WebSocket Response: Runtime.evaluate (id=25) (session_id=999625059EF0CFFCD977585239D7DA4A) 168CC86AE53E1EF23996F16DA2F40D4E {
   "result": {
      "description": "1",
      "type": "number",
      "value": 1
   }
}

Jibri 2025-07-08 12:41:12.876 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T12:41:12+0000] [INFO] Done waiting for pending navigations. Status: ok

Jibri 2025-07-08 12:41:12.876 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T12:41:12+0000] [INFO] [f542fb5ab80b3baad445754e5e802ece] RESPONSE Navigate ERROR unknown error: net::ERR_CONNECTION_REFUSED
  (Session info: chrome=130.0.6723.116)

Jibri 2025-07-08 12:41:12.876 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T12:41:12+0000] [INFO] [f542fb5ab80b3baad445754e5e802ece] COMMAND GetLogTypes {
}

Jibri 2025-07-08 12:41:12.877 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T12:41:12+0000] [INFO] [f542fb5ab80b3baad445754e5e802ece] RESPONSE GetLogTypes [ "browser", "driver" ]

Jibri 2025-07-08 12:41:12.877 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T12:41:12+0000] [INFO] [f542fb5ab80b3baad445754e5e802ece] COMMAND GetLog {
   "type": "browser"
}

Jibri 2025-07-08 12:41:12.877 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T12:41:12+0000] [FINE] DevTools WebSocket Command: Runtime.evaluate (id=26) (session_id=999625059EF0CFFCD977585239D7DA4A) 168CC86AE53E1EF23996F16DA2F40D4E {
   "awaitPromise": false,
   "expression": "1",
   "returnByValue": true
}

Jibri 2025-07-08 12:41:12.877 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T12:41:12+0000] [FINE] DevTools WebSocket Response: Runtime.evaluate (id=26) (session_id=999625059EF0CFFCD977585239D7DA4A) 168CC86AE53E1EF23996F16DA2F40D4E {
   "result": {
      "description": "1",
      "type": "number",
      "value": 1
   }
}

Jibri 2025-07-08 12:41:12.878 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T12:41:12+0000] [INFO] [f542fb5ab80b3baad445754e5e802ece] RESPONSE GetLog [  ]

Jibri 2025-07-08 12:41:12.878 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T12:41:12+0000] [INFO] [f542fb5ab80b3baad445754e5e802ece] COMMAND GetLog {
   "type": "driver"
}

Jibri 2025-07-08 12:41:12.878 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T12:41:12+0000] [FINE] DevTools WebSocket Command: Runtime.evaluate (id=27) (session_id=999625059EF0CFFCD977585239D7DA4A) 168CC86AE53E1EF23996F16DA2F40D4E {
   "awaitPromise": false,
   "expression": "1",
   "returnByValue": true
}

Jibri 2025-07-08 12:41:12.878 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T12:41:12+0000] [FINE] DevTools WebSocket Response: Runtime.evaluate (id=27) (session_id=999625059EF0CFFCD977585239D7DA4A) 168CC86AE53E1EF23996F16DA2F40D4E {
   "result": {
      "description": "1",
      "type": "number",
      "value": 1
   }
}

Jibri 2025-07-08 12:41:12.879 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#354: ========= TYPE=client ===========
