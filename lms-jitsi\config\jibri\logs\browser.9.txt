Jibri 2025-07-08 11:25:33.615 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#349: Logs for call null
Jibri 2025-07-08 11:25:33.628 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#354: ========= TYPE=browser ===========
Jibri 2025-07-08 11:25:33.641 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#354: ========= TYPE=driver ===========
Jibri 2025-07-08 11:25:33.642 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T11:25:33+0000] [INFO] Browser search. Trying... /usr/bin/chrome

Jibri 2025-07-08 11:25:33.642 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T11:25:33+0000] [INFO] Browser search. Trying... /usr/bin/chrome

Jibri 2025-07-08 11:25:33.643 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T11:25:33+0000] [INFO] Browser search. Trying... /usr/bin/google-chrome

Jibri 2025-07-08 11:25:33.643 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T11:25:33+0000] [INFO] Browser search. Found at  /usr/bin/google-chrome

Jibri 2025-07-08 11:25:33.643 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T11:25:33+0000] [INFO] Populating Preferences file: {
   "alternate_error_pages": {
      "enabled": false
   },
   "autofill": {
      "enabled": false
   },
   "browser": {
      "check_default_browser": false
   },
   "distribution": {
      "import_bookmarks": false,
      "import_history": false,
      "import_search_engine": false,
      "make_chrome_default_for_user": false,
      "skip_first_run_ui": true
   },
   "dns_prefetching": {
      "enabled": false
   },
   "profile": {
      "content_settings": {
         "pattern_pairs": {
            "https://*,*": {
               "media-stream": {
                  "audio": "Default",
                  "video": "Default"
               }
            }
         }
      },
      "default_content_setting_values": {
         "geolocation": 1
      },
      "default_content_settings": {
         "geolocation": 1,
         "mouselock": 1,
         "notifications": 1,
         "popups": 1,
         "ppapi-broker": 1
      },
      "password_manager_enabled": false
   },
   "safebrowsing": {
      "enabled": false
   },
   "search": {
      "suggest_enabled": false
   },
   "translate": {
      "enabled": false
   }
}

Jibri 2025-07-08 11:25:33.643 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T11:25:33+0000] [INFO] Populating Local State file: {
   "background_mode": {
      "enabled": false
   },
   "ssl": {
      "rev_checking": {
         "enabled": false
      }
   }
}

Jibri 2025-07-08 11:25:33.644 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T11:25:33+0000] [INFO] ChromeDriver supports communication with Chrome via pipes. This is more reliable and more secure.

Jibri 2025-07-08 11:25:33.644 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T11:25:33+0000] [INFO] Use the --remote-debugging-pipe Chrome switch instead of the default --remote-debugging-port to enable this communication mode.

Jibri 2025-07-08 11:25:33.644 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T11:25:33+0000] [INFO] Launching chrome: /usr/bin/google-chrome --allow-pre-commit-input --autoplay-policy=no-user-gesture-required --disable-background-networking --disable-client-side-phishing-detection --disable-default-apps --disable-hang-monitor --disable-popup-blocking --disable-prompt-on-repost --disable-sync --enable-automation --enable-logging --enabled --kiosk --log-level=0 --no-first-run --no-service-autorun --password-store=basic --remote-debugging-port=0 --start-maximized --test-type=webdriver --use-fake-ui-for-media-stream --use-mock-keychain --user-data-dir=/tmp/.org.chromium.Chromium.IZKpy0 data:,

Jibri 2025-07-08 11:25:33.644 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T11:25:33+0000] [FINE] DevTools HTTP Request: http://localhost:37541/json/version

Jibri 2025-07-08 11:25:33.645 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T11:25:33+0000] [FINE] DevTools HTTP Response: {
   "Browser": "Chrome/130.0.6723.116",
   "Protocol-Version": "1.3",
   "User-Agent": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36",
   "V8-Version": "***********",
   "WebKit-Version": "537.36 (@6ac35f94ae3d01152cf1946c896b0678e48f8ec4)",
   "webSocketDebuggerUrl": "ws://localhost:37541/devtools/browser/348b3e96-546d-4071-972b-ac86d7dd804a"
}


Jibri 2025-07-08 11:25:33.645 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T11:25:33+0000] [FINE] DevTools HTTP Request: http://localhost:37541/json/list

Jibri 2025-07-08 11:25:33.645 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T11:25:33+0000] [FINE] DevTools HTTP Response: [ {
   "description": "",
   "devtoolsFrontendUrl": "/devtools/inspector.html?ws=localhost:37541/devtools/page/D3674DA1CAAEC7ECD26D687E55D0BE74",
   "id": "D3674DA1CAAEC7ECD26D687E55D0BE74",
   "title": "",
   "type": "page",
   "url": "data:,",
   "webSocketDebuggerUrl": "ws://localhost:37541/devtools/page/D3674DA1CAAEC7ECD26D687E55D0BE74"
} ]


Jibri 2025-07-08 11:25:33.645 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T11:25:33+0000] [FINE] DevTools WebSocket Command: Target.getTargets (id=1) (session_id=) browser {
}

Jibri 2025-07-08 11:25:33.646 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T11:25:33+0000] [FINE] DevTools WebSocket Response: Target.getTargets (id=1) (session_id=) browser {
   "targetInfos": [ {
      "attached": false,
      "browserContextId": "1F3A6ED6FA48F48221ADF2B1DB7BF1BD",
      "canAccessOpener": false,
      "targetId": "D3674DA1CAAEC7ECD26D687E55D0BE74",
      "title": "",
      "type": "page",
      "url": "data:,"
   } ]
}

Jibri 2025-07-08 11:25:33.646 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T11:25:33+0000] [FINE] DevTools WebSocket Command: Target.attachToTarget (id=2) (session_id=) browser {
   "flatten": true,
   "targetId": "D3674DA1CAAEC7ECD26D687E55D0BE74"
}

Jibri 2025-07-08 11:25:33.646 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T11:25:33+0000] [FINE] DevTools WebSocket Event: Target.attachedToTarget (session_id=) browser {
   "sessionId": "83FA31A08373B5CABD189FDA565F17A4",
   "targetInfo": {
      "attached": true,
      "browserContextId": "1F3A6ED6FA48F48221ADF2B1DB7BF1BD",
      "canAccessOpener": false,
      "targetId": "D3674DA1CAAEC7ECD26D687E55D0BE74",
      "title": "",
      "type": "page",
      "url": "data:,"
   },
   "waitingForDebugger": false
}

Jibri 2025-07-08 11:25:33.647 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T11:25:33+0000] [FINE] DevTools WebSocket Response: Target.attachToTarget (id=2) (session_id=) browser {
   "sessionId": "83FA31A08373B5CABD189FDA565F17A4"
}

Jibri 2025-07-08 11:25:33.647 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T11:25:33+0000] [FINE] DevTools WebSocket Command: Page.enable (id=3) (session_id=83FA31A08373B5CABD189FDA565F17A4) D3674DA1CAAEC7ECD26D687E55D0BE74 {
}

Jibri 2025-07-08 11:25:33.647 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T11:25:33+0000] [FINE] DevTools WebSocket Command: Page.addScriptToEvaluateOnNewDocument (id=4) (session_id=83FA31A08373B5CABD189FDA565F17A4) D3674DA1CAAEC7ECD26D687E55D0BE74 {
   "source": "(function () {window.cdc_adoQpoasnfa76pfcZLmcfl_Array = window.Array;window.cdc_adoQpoasnfa76pfcZLmcfl_Object = window.Object;window.cdc_adoQpoasnfa76pfcZLmcfl_Promise = window.Promise;window.cdc_a..."
}

Jibri 2025-07-08 11:25:33.647 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T11:25:33+0000] [FINE] DevTools WebSocket Command: Runtime.evaluate (id=5) (session_id=83FA31A08373B5CABD189FDA565F17A4) D3674DA1CAAEC7ECD26D687E55D0BE74 {
   "expression": "(function () {window.cdc_adoQpoasnfa76pfcZLmcfl_Array = window.Array;window.cdc_adoQpoasnfa76pfcZLmcfl_Object = window.Object;window.cdc_adoQpoasnfa76pfcZLmcfl_Promise = window.Promise;window.cdc_a..."
}

Jibri 2025-07-08 11:25:33.648 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T11:25:33+0000] [FINE] DevTools WebSocket Command: Log.enable (id=6) (session_id=83FA31A08373B5CABD189FDA565F17A4) D3674DA1CAAEC7ECD26D687E55D0BE74 {
}

Jibri 2025-07-08 11:25:33.648 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T11:25:33+0000] [FINE] DevTools WebSocket Command: Target.setAutoAttach (id=7) (session_id=83FA31A08373B5CABD189FDA565F17A4) D3674DA1CAAEC7ECD26D687E55D0BE74 {
   "autoAttach": true,
   "flatten": true,
   "waitForDebuggerOnStart": false
}

Jibri 2025-07-08 11:25:33.648 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T11:25:33+0000] [FINE] DevTools WebSocket Response: Page.enable (id=3) (session_id=83FA31A08373B5CABD189FDA565F17A4) D3674DA1CAAEC7ECD26D687E55D0BE74 {
}

Jibri 2025-07-08 11:25:33.648 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T11:25:33+0000] [FINE] DevTools WebSocket Response: Page.addScriptToEvaluateOnNewDocument (id=4) (session_id=83FA31A08373B5CABD189FDA565F17A4) D3674DA1CAAEC7ECD26D687E55D0BE74 {
   "identifier": "1"
}

Jibri 2025-07-08 11:25:33.648 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T11:25:33+0000] [FINE] DevTools WebSocket Response: Runtime.evaluate (id=5) (session_id=83FA31A08373B5CABD189FDA565F17A4) D3674DA1CAAEC7ECD26D687E55D0BE74 {
   "result": {
      "type": "undefined"
   }
}

Jibri 2025-07-08 11:25:33.649 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T11:25:33+0000] [FINE] DevTools WebSocket Response: Log.enable (id=6) (session_id=83FA31A08373B5CABD189FDA565F17A4) D3674DA1CAAEC7ECD26D687E55D0BE74 {
}

Jibri 2025-07-08 11:25:33.649 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T11:25:33+0000] [FINE] DevTools WebSocket Response: Target.setAutoAttach (id=7) (session_id=83FA31A08373B5CABD189FDA565F17A4) D3674DA1CAAEC7ECD26D687E55D0BE74 {
}

Jibri 2025-07-08 11:25:33.649 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T11:25:33+0000] [FINE] DevTools WebSocket Command: Runtime.enable (id=8) (session_id=83FA31A08373B5CABD189FDA565F17A4) D3674DA1CAAEC7ECD26D687E55D0BE74 {
}

Jibri 2025-07-08 11:25:33.649 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T11:25:33+0000] [FINE] DevTools WebSocket Event: Page.domContentEventFired (session_id=83FA31A08373B5CABD189FDA565F17A4) D3674DA1CAAEC7ECD26D687E55D0BE74 {
   "timestamp": 5000.242763
}

Jibri 2025-07-08 11:25:33.650 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T11:25:33+0000] [FINE] DevTools WebSocket Event: Page.loadEventFired (session_id=83FA31A08373B5CABD189FDA565F17A4) D3674DA1CAAEC7ECD26D687E55D0BE74 {
   "timestamp": 5000.243254
}

Jibri 2025-07-08 11:25:33.650 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T11:25:33+0000] [FINE] DevTools WebSocket Event: Page.frameStoppedLoading (session_id=83FA31A08373B5CABD189FDA565F17A4) D3674DA1CAAEC7ECD26D687E55D0BE74 {
   "frameId": "D3674DA1CAAEC7ECD26D687E55D0BE74"
}

Jibri 2025-07-08 11:25:33.650 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T11:25:33+0000] [FINE] DevTools WebSocket Event: Page.frameResized (session_id=83FA31A08373B5CABD189FDA565F17A4) D3674DA1CAAEC7ECD26D687E55D0BE74 {
}

Jibri 2025-07-08 11:25:33.650 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T11:25:33+0000] [FINE] DevTools WebSocket Event: Runtime.executionContextCreated (session_id=83FA31A08373B5CABD189FDA565F17A4) D3674DA1CAAEC7ECD26D687E55D0BE74 {
   "context": {
      "auxData": {
         "frameId": "D3674DA1CAAEC7ECD26D687E55D0BE74",
         "isDefault": true,
         "type": "default"
      },
      "id": 1,
      "name": "",
      "origin": "://",
      "uniqueId": "9078204664229758052.-3739569931758353158"
   }
}

Jibri 2025-07-08 11:25:33.650 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T11:25:33+0000] [FINE] DevTools WebSocket Response: Runtime.enable (id=8) (session_id=83FA31A08373B5CABD189FDA565F17A4) D3674DA1CAAEC7ECD26D687E55D0BE74 {
}

Jibri 2025-07-08 11:25:33.651 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T11:25:33+0000] [FINE] DevTools WebSocket Command: Runtime.enable (id=9) (session_id=83FA31A08373B5CABD189FDA565F17A4) D3674DA1CAAEC7ECD26D687E55D0BE74 {
}

Jibri 2025-07-08 11:25:33.651 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T11:25:33+0000] [FINE] DevTools WebSocket Response: Runtime.enable (id=9) (session_id=83FA31A08373B5CABD189FDA565F17A4) D3674DA1CAAEC7ECD26D687E55D0BE74 {
}

Jibri 2025-07-08 11:25:33.651 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T11:25:33+0000] [INFO] [e83271e33e707f07e91d440b1a834fa0] RESPONSE InitSession {
   "acceptInsecureCerts": false,
   "acceptSslCerts": false,
   "browserConnectionEnabled": false,
   "browserName": "chrome",
   "chrome": {
      "chromedriverVersion": "130.0.6723.116 (6ac35f94ae3d01152cf1946c896b0678e48f8ec4-refs/branch-heads/6723@{#1764})",
      "userDataDir": "/tmp/.org.chromium.Chromium.IZKpy0"
   },
   "cssSelectorsEnabled": true,
   "databaseEnabled": false,
   "fedcm:accounts": true,
   "goog:chromeOptions": {
      "debuggerAddress": "localhost:37541"
   },
   "handlesAlerts": true,
   "hasTouchScreen": false,
   "javascriptEnabled": true,
   "locationContextEnabled": true,
   "mobileEmulationEnabled": false,
   "nativeEvents": true,
   "networkConnectionEnabled": false,
   "pageLoadStrategy": "normal",
   "platform": "Linux",
   "proxy": {
   },
   "~~~": "..."
}

Jibri 2025-07-08 11:25:33.651 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T11:25:33+0000] [INFO] [e83271e33e707f07e91d440b1a834fa0] COMMAND SetTimeouts {
   "ms": 60000,
   "type": "page load"
}

Jibri 2025-07-08 11:25:33.651 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T11:25:33+0000] [INFO] [e83271e33e707f07e91d440b1a834fa0] RESPONSE SetTimeouts

Jibri 2025-07-08 11:25:33.652 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T11:25:33+0000] [INFO] [e83271e33e707f07e91d440b1a834fa0] COMMAND Navigate {
   "url": "http://web:8081/"
}

Jibri 2025-07-08 11:25:33.652 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T11:25:33+0000] [INFO] Waiting for pending navigations...

Jibri 2025-07-08 11:25:33.652 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T11:25:33+0000] [FINE] DevTools WebSocket Command: Runtime.evaluate (id=10) (session_id=83FA31A08373B5CABD189FDA565F17A4) D3674DA1CAAEC7ECD26D687E55D0BE74 {
   "expression": "1"
}

Jibri 2025-07-08 11:25:33.652 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T11:25:33+0000] [FINE] DevTools WebSocket Response: Runtime.evaluate (id=10) (session_id=83FA31A08373B5CABD189FDA565F17A4) D3674DA1CAAEC7ECD26D687E55D0BE74 {
   "result": {
      "description": "1",
      "type": "number",
      "value": 1
   }
}

Jibri 2025-07-08 11:25:33.652 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T11:25:33+0000] [INFO] Done waiting for pending navigations. Status: ok

Jibri 2025-07-08 11:25:33.653 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T11:25:33+0000] [FINE] DevTools WebSocket Command: Page.navigate (id=11) (session_id=83FA31A08373B5CABD189FDA565F17A4) D3674DA1CAAEC7ECD26D687E55D0BE74 {
   "url": "http://web:8081/"
}

Jibri 2025-07-08 11:25:33.653 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T11:25:33+0000] [FINE] DevTools WebSocket Event: Page.frameStartedLoading (session_id=83FA31A08373B5CABD189FDA565F17A4) D3674DA1CAAEC7ECD26D687E55D0BE74 {
   "frameId": "D3674DA1CAAEC7ECD26D687E55D0BE74"
}

Jibri 2025-07-08 11:25:33.653 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T11:25:33+0000] [FINE] DevTools WebSocket Response: Page.navigate (id=11) (session_id=83FA31A08373B5CABD189FDA565F17A4) D3674DA1CAAEC7ECD26D687E55D0BE74 {
   "errorText": "net::ERR_CONNECTION_REFUSED",
   "frameId": "D3674DA1CAAEC7ECD26D687E55D0BE74",
   "loaderId": "727ACB5CF279AEC715E27E8AA633CF24"
}

Jibri 2025-07-08 11:25:33.653 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T11:25:33+0000] [FINE] DevTools WebSocket Command: Runtime.evaluate (id=12) (session_id=83FA31A08373B5CABD189FDA565F17A4) D3674DA1CAAEC7ECD26D687E55D0BE74 {
   "expression": "1"
}

Jibri 2025-07-08 11:25:33.653 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T11:25:33+0000] [FINE] DevTools WebSocket Event: Runtime.executionContextsCleared (session_id=83FA31A08373B5CABD189FDA565F17A4) D3674DA1CAAEC7ECD26D687E55D0BE74 {
}

Jibri 2025-07-08 11:25:33.654 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T11:25:33+0000] [FINE] DevTools WebSocket Event: Runtime.executionContextsCleared (session_id=83FA31A08373B5CABD189FDA565F17A4) D3674DA1CAAEC7ECD26D687E55D0BE74 {
}

Jibri 2025-07-08 11:25:33.654 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T11:25:33+0000] [FINE] DevTools WebSocket Event: Page.frameNavigated (session_id=83FA31A08373B5CABD189FDA565F17A4) D3674DA1CAAEC7ECD26D687E55D0BE74 {
   "frame": {
      "adFrameStatus": {
         "adFrameType": "none"
      },
      "crossOriginIsolatedContextType": "NotIsolated",
      "domainAndRegistry": "",
      "gatedAPIFeatures": [  ],
      "id": "D3674DA1CAAEC7ECD26D687E55D0BE74",
      "loaderId": "FF42E7693AAC4B90FF1B27517E3F14D9",
      "mimeType": "text/html",
      "secureContextType": "InsecureScheme",
      "securityOrigin": "://",
      "unreachableUrl": "http://web:8081/",
      "url": "chrome-error://chromewebdata/"
   },
   "type": "Navigation"
}

Jibri 2025-07-08 11:25:33.654 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T11:25:33+0000] [FINE] DevTools WebSocket Event: Runtime.executionContextCreated (session_id=83FA31A08373B5CABD189FDA565F17A4) D3674DA1CAAEC7ECD26D687E55D0BE74 {
   "context": {
      "auxData": {
         "frameId": "D3674DA1CAAEC7ECD26D687E55D0BE74",
         "isDefault": true,
         "type": "default"
      },
      "id": 1,
      "name": "",
      "origin": "://",
      "uniqueId": "-2393993130030354715.5128178520520607712"
   }
}

Jibri 2025-07-08 11:25:33.654 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T11:25:33+0000] [FINE] DevTools WebSocket Response: Runtime.evaluate (id=12) (session_id=83FA31A08373B5CABD189FDA565F17A4) D3674DA1CAAEC7ECD26D687E55D0BE74 {
   "result": {
      "description": "1",
      "type": "number",
      "value": 1
   }
}

Jibri 2025-07-08 11:25:33.655 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T11:25:33+0000] [INFO] Waiting for pending navigations...

Jibri 2025-07-08 11:25:33.655 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T11:25:33+0000] [FINE] DevTools WebSocket Command: Runtime.evaluate (id=13) (session_id=83FA31A08373B5CABD189FDA565F17A4) D3674DA1CAAEC7ECD26D687E55D0BE74 {
   "expression": "1"
}

Jibri 2025-07-08 11:25:33.655 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T11:25:33+0000] [FINE] DevTools WebSocket Response: Runtime.evaluate (id=13) (session_id=83FA31A08373B5CABD189FDA565F17A4) D3674DA1CAAEC7ECD26D687E55D0BE74 {
   "result": {
      "description": "1",
      "type": "number",
      "value": 1
   }
}

Jibri 2025-07-08 11:25:33.656 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T11:25:33+0000] [FINE] DevTools WebSocket Event: Page.domContentEventFired (session_id=83FA31A08373B5CABD189FDA565F17A4) D3674DA1CAAEC7ECD26D687E55D0BE74 {
   "timestamp": 5000.355166
}

Jibri 2025-07-08 11:25:33.656 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T11:25:33+0000] [FINE] DevTools WebSocket Command: Runtime.evaluate (id=14) (session_id=83FA31A08373B5CABD189FDA565F17A4) D3674DA1CAAEC7ECD26D687E55D0BE74 {
   "expression": "1"
}

Jibri 2025-07-08 11:25:33.656 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T11:25:33+0000] [FINE] DevTools WebSocket Event: Page.loadEventFired (session_id=83FA31A08373B5CABD189FDA565F17A4) D3674DA1CAAEC7ECD26D687E55D0BE74 {
   "timestamp": 5000.365095
}

Jibri 2025-07-08 11:25:33.657 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T11:25:33+0000] [FINE] DevTools WebSocket Command: Runtime.evaluate (id=15) (session_id=83FA31A08373B5CABD189FDA565F17A4) D3674DA1CAAEC7ECD26D687E55D0BE74 {
   "awaitPromise": false,
   "expression": "document.readyState",
   "returnByValue": true,
   "uniqueContextId": "-2393993130030354715.5128178520520607712"
}

Jibri 2025-07-08 11:25:33.657 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T11:25:33+0000] [FINE] DevTools WebSocket Event: Page.frameStoppedLoading (session_id=83FA31A08373B5CABD189FDA565F17A4) D3674DA1CAAEC7ECD26D687E55D0BE74 {
   "frameId": "D3674DA1CAAEC7ECD26D687E55D0BE74"
}

Jibri 2025-07-08 11:25:33.657 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T11:25:33+0000] [FINE] DevTools WebSocket Response: Runtime.evaluate (id=14) (session_id=83FA31A08373B5CABD189FDA565F17A4) D3674DA1CAAEC7ECD26D687E55D0BE74 {
   "result": {
      "description": "1",
      "type": "number",
      "value": 1
   }
}

Jibri 2025-07-08 11:25:33.658 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T11:25:33+0000] [FINE] DevTools WebSocket Response: Runtime.evaluate (id=15) (session_id=83FA31A08373B5CABD189FDA565F17A4) D3674DA1CAAEC7ECD26D687E55D0BE74 {
   "result": {
      "type": "string",
      "value": "complete"
   }
}

Jibri 2025-07-08 11:25:33.658 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T11:25:33+0000] [INFO] Done waiting for pending navigations. Status: ok

Jibri 2025-07-08 11:25:33.658 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T11:25:33+0000] [FINE] DevTools WebSocket Command: Page.navigate (id=16) (session_id=83FA31A08373B5CABD189FDA565F17A4) D3674DA1CAAEC7ECD26D687E55D0BE74 {
   "url": "http://web:8081/"
}

Jibri 2025-07-08 11:25:33.658 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T11:25:33+0000] [FINE] DevTools WebSocket Event: Page.frameStartedLoading (session_id=83FA31A08373B5CABD189FDA565F17A4) D3674DA1CAAEC7ECD26D687E55D0BE74 {
   "frameId": "D3674DA1CAAEC7ECD26D687E55D0BE74"
}

Jibri 2025-07-08 11:25:33.659 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T11:25:33+0000] [FINE] DevTools WebSocket Response: Page.navigate (id=16) (session_id=83FA31A08373B5CABD189FDA565F17A4) D3674DA1CAAEC7ECD26D687E55D0BE74 {
   "errorText": "net::ERR_CONNECTION_REFUSED",
   "frameId": "D3674DA1CAAEC7ECD26D687E55D0BE74",
   "loaderId": "F515C79313F1C3A4E05AD9798854E446"
}

Jibri 2025-07-08 11:25:33.659 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T11:25:33+0000] [FINE] DevTools WebSocket Command: Runtime.evaluate (id=17) (session_id=83FA31A08373B5CABD189FDA565F17A4) D3674DA1CAAEC7ECD26D687E55D0BE74 {
   "expression": "1"
}

Jibri 2025-07-08 11:25:33.659 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T11:25:33+0000] [FINE] DevTools WebSocket Event: Runtime.executionContextsCleared (session_id=83FA31A08373B5CABD189FDA565F17A4) D3674DA1CAAEC7ECD26D687E55D0BE74 {
}

Jibri 2025-07-08 11:25:33.659 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T11:25:33+0000] [FINE] DevTools WebSocket Event: Page.frameNavigated (session_id=83FA31A08373B5CABD189FDA565F17A4) D3674DA1CAAEC7ECD26D687E55D0BE74 {
   "frame": {
      "adFrameStatus": {
         "adFrameType": "none"
      },
      "crossOriginIsolatedContextType": "NotIsolated",
      "domainAndRegistry": "",
      "gatedAPIFeatures": [  ],
      "id": "D3674DA1CAAEC7ECD26D687E55D0BE74",
      "loaderId": "E60E189BD1414A79AF9416F83AA70A3F",
      "mimeType": "text/html",
      "secureContextType": "InsecureScheme",
      "securityOrigin": "://",
      "unreachableUrl": "http://web:8081/",
      "url": "chrome-error://chromewebdata/"
   },
   "type": "Navigation"
}

Jibri 2025-07-08 11:25:33.660 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T11:25:33+0000] [FINE] DevTools WebSocket Event: Runtime.executionContextCreated (session_id=83FA31A08373B5CABD189FDA565F17A4) D3674DA1CAAEC7ECD26D687E55D0BE74 {
   "context": {
      "auxData": {
         "frameId": "D3674DA1CAAEC7ECD26D687E55D0BE74",
         "isDefault": true,
         "type": "default"
      },
      "id": 2,
      "name": "",
      "origin": "://",
      "uniqueId": "-1072645669698751656.8224055754000539352"
   }
}

Jibri 2025-07-08 11:25:33.660 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T11:25:33+0000] [FINE] DevTools WebSocket Response: Runtime.evaluate (id=17) (session_id=83FA31A08373B5CABD189FDA565F17A4) D3674DA1CAAEC7ECD26D687E55D0BE74 {
   "result": {
      "description": "1",
      "type": "number",
      "value": 1
   }
}

Jibri 2025-07-08 11:25:33.660 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T11:25:33+0000] [INFO] Waiting for pending navigations...

Jibri 2025-07-08 11:25:33.660 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T11:25:33+0000] [FINE] DevTools WebSocket Command: Runtime.evaluate (id=18) (session_id=83FA31A08373B5CABD189FDA565F17A4) D3674DA1CAAEC7ECD26D687E55D0BE74 {
   "expression": "1"
}

Jibri 2025-07-08 11:25:33.660 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T11:25:33+0000] [FINE] DevTools WebSocket Event: Page.domContentEventFired (session_id=83FA31A08373B5CABD189FDA565F17A4) D3674DA1CAAEC7ECD26D687E55D0BE74 {
   "timestamp": 5000.386891
}

Jibri 2025-07-08 11:25:33.661 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T11:25:33+0000] [FINE] DevTools WebSocket Response: Runtime.evaluate (id=18) (session_id=83FA31A08373B5CABD189FDA565F17A4) D3674DA1CAAEC7ECD26D687E55D0BE74 {
   "result": {
      "description": "1",
      "type": "number",
      "value": 1
   }
}

Jibri 2025-07-08 11:25:33.661 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T11:25:33+0000] [FINE] DevTools WebSocket Event: Page.loadEventFired (session_id=83FA31A08373B5CABD189FDA565F17A4) D3674DA1CAAEC7ECD26D687E55D0BE74 {
   "timestamp": 5000.387909
}

Jibri 2025-07-08 11:25:33.661 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T11:25:33+0000] [FINE] DevTools WebSocket Command: Runtime.evaluate (id=19) (session_id=83FA31A08373B5CABD189FDA565F17A4) D3674DA1CAAEC7ECD26D687E55D0BE74 {
   "awaitPromise": false,
   "expression": "document.readyState",
   "returnByValue": true,
   "uniqueContextId": "-1072645669698751656.8224055754000539352"
}

Jibri 2025-07-08 11:25:33.661 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T11:25:33+0000] [FINE] DevTools WebSocket Event: Page.frameStoppedLoading (session_id=83FA31A08373B5CABD189FDA565F17A4) D3674DA1CAAEC7ECD26D687E55D0BE74 {
   "frameId": "D3674DA1CAAEC7ECD26D687E55D0BE74"
}

Jibri 2025-07-08 11:25:33.662 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T11:25:33+0000] [FINE] DevTools WebSocket Response: Runtime.evaluate (id=19) (session_id=83FA31A08373B5CABD189FDA565F17A4) D3674DA1CAAEC7ECD26D687E55D0BE74 {
   "result": {
      "type": "string",
      "value": "complete"
   }
}

Jibri 2025-07-08 11:25:33.662 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T11:25:33+0000] [FINE] DevTools WebSocket Command: Runtime.evaluate (id=20) (session_id=83FA31A08373B5CABD189FDA565F17A4) D3674DA1CAAEC7ECD26D687E55D0BE74 {
   "expression": "1"
}

Jibri 2025-07-08 11:25:33.662 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T11:25:33+0000] [FINE] DevTools WebSocket Response: Runtime.evaluate (id=20) (session_id=83FA31A08373B5CABD189FDA565F17A4) D3674DA1CAAEC7ECD26D687E55D0BE74 {
   "result": {
      "description": "1",
      "type": "number",
      "value": 1
   }
}

Jibri 2025-07-08 11:25:33.662 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T11:25:33+0000] [INFO] Done waiting for pending navigations. Status: ok

Jibri 2025-07-08 11:25:33.663 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T11:25:33+0000] [FINE] DevTools WebSocket Command: Page.navigate (id=21) (session_id=83FA31A08373B5CABD189FDA565F17A4) D3674DA1CAAEC7ECD26D687E55D0BE74 {
   "url": "http://web:8081/"
}

Jibri 2025-07-08 11:25:33.663 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T11:25:33+0000] [FINE] DevTools WebSocket Event: Page.frameStartedLoading (session_id=83FA31A08373B5CABD189FDA565F17A4) D3674DA1CAAEC7ECD26D687E55D0BE74 {
   "frameId": "D3674DA1CAAEC7ECD26D687E55D0BE74"
}

Jibri 2025-07-08 11:25:33.663 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T11:25:33+0000] [FINE] DevTools WebSocket Response: Page.navigate (id=21) (session_id=83FA31A08373B5CABD189FDA565F17A4) D3674DA1CAAEC7ECD26D687E55D0BE74 {
   "errorText": "net::ERR_CONNECTION_REFUSED",
   "frameId": "D3674DA1CAAEC7ECD26D687E55D0BE74",
   "loaderId": "E468D02491C74C7DC68BCE9E6AD730D3"
}

Jibri 2025-07-08 11:25:33.663 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T11:25:33+0000] [FINE] DevTools WebSocket Command: Runtime.evaluate (id=22) (session_id=83FA31A08373B5CABD189FDA565F17A4) D3674DA1CAAEC7ECD26D687E55D0BE74 {
   "expression": "1"
}

Jibri 2025-07-08 11:25:33.664 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T11:25:33+0000] [FINE] DevTools WebSocket Event: Runtime.executionContextsCleared (session_id=83FA31A08373B5CABD189FDA565F17A4) D3674DA1CAAEC7ECD26D687E55D0BE74 {
}

Jibri 2025-07-08 11:25:33.664 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T11:25:33+0000] [FINE] DevTools WebSocket Event: Page.frameNavigated (session_id=83FA31A08373B5CABD189FDA565F17A4) D3674DA1CAAEC7ECD26D687E55D0BE74 {
   "frame": {
      "adFrameStatus": {
         "adFrameType": "none"
      },
      "crossOriginIsolatedContextType": "NotIsolated",
      "domainAndRegistry": "",
      "gatedAPIFeatures": [  ],
      "id": "D3674DA1CAAEC7ECD26D687E55D0BE74",
      "loaderId": "38E7318C3E654698C37BA8D792475E05",
      "mimeType": "text/html",
      "secureContextType": "InsecureScheme",
      "securityOrigin": "://",
      "unreachableUrl": "http://web:8081/",
      "url": "chrome-error://chromewebdata/"
   },
   "type": "Navigation"
}

Jibri 2025-07-08 11:25:33.664 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T11:25:33+0000] [FINE] DevTools WebSocket Event: Runtime.executionContextCreated (session_id=83FA31A08373B5CABD189FDA565F17A4) D3674DA1CAAEC7ECD26D687E55D0BE74 {
   "context": {
      "auxData": {
         "frameId": "D3674DA1CAAEC7ECD26D687E55D0BE74",
         "isDefault": true,
         "type": "default"
      },
      "id": 3,
      "name": "",
      "origin": "://",
      "uniqueId": "4405080319986636332.-3083191369664363042"
   }
}

Jibri 2025-07-08 11:25:33.664 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T11:25:33+0000] [FINE] DevTools WebSocket Response: Runtime.evaluate (id=22) (session_id=83FA31A08373B5CABD189FDA565F17A4) D3674DA1CAAEC7ECD26D687E55D0BE74 {
   "result": {
      "description": "1",
      "type": "number",
      "value": 1
   }
}

Jibri 2025-07-08 11:25:33.664 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T11:25:33+0000] [INFO] Waiting for pending navigations...

Jibri 2025-07-08 11:25:33.665 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T11:25:33+0000] [FINE] DevTools WebSocket Command: Runtime.evaluate (id=23) (session_id=83FA31A08373B5CABD189FDA565F17A4) D3674DA1CAAEC7ECD26D687E55D0BE74 {
   "expression": "1"
}

Jibri 2025-07-08 11:25:33.665 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T11:25:33+0000] [FINE] DevTools WebSocket Event: Page.domContentEventFired (session_id=83FA31A08373B5CABD189FDA565F17A4) D3674DA1CAAEC7ECD26D687E55D0BE74 {
   "timestamp": 5000.416283
}

Jibri 2025-07-08 11:25:33.665 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T11:25:33+0000] [FINE] DevTools WebSocket Response: Runtime.evaluate (id=23) (session_id=83FA31A08373B5CABD189FDA565F17A4) D3674DA1CAAEC7ECD26D687E55D0BE74 {
   "result": {
      "description": "1",
      "type": "number",
      "value": 1
   }
}

Jibri 2025-07-08 11:25:33.665 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T11:25:33+0000] [FINE] DevTools WebSocket Event: Page.loadEventFired (session_id=83FA31A08373B5CABD189FDA565F17A4) D3674DA1CAAEC7ECD26D687E55D0BE74 {
   "timestamp": 5000.417335
}

Jibri 2025-07-08 11:25:33.665 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T11:25:33+0000] [FINE] DevTools WebSocket Command: Runtime.evaluate (id=24) (session_id=83FA31A08373B5CABD189FDA565F17A4) D3674DA1CAAEC7ECD26D687E55D0BE74 {
   "awaitPromise": false,
   "expression": "document.readyState",
   "returnByValue": true,
   "uniqueContextId": "4405080319986636332.-3083191369664363042"
}

Jibri 2025-07-08 11:25:33.666 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T11:25:33+0000] [FINE] DevTools WebSocket Event: Page.frameStoppedLoading (session_id=83FA31A08373B5CABD189FDA565F17A4) D3674DA1CAAEC7ECD26D687E55D0BE74 {
   "frameId": "D3674DA1CAAEC7ECD26D687E55D0BE74"
}

Jibri 2025-07-08 11:25:33.666 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T11:25:33+0000] [FINE] DevTools WebSocket Response: Runtime.evaluate (id=24) (session_id=83FA31A08373B5CABD189FDA565F17A4) D3674DA1CAAEC7ECD26D687E55D0BE74 {
   "result": {
      "type": "string",
      "value": "complete"
   }
}

Jibri 2025-07-08 11:25:33.666 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T11:25:33+0000] [FINE] DevTools WebSocket Command: Runtime.evaluate (id=25) (session_id=83FA31A08373B5CABD189FDA565F17A4) D3674DA1CAAEC7ECD26D687E55D0BE74 {
   "expression": "1"
}

Jibri 2025-07-08 11:25:33.666 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T11:25:33+0000] [FINE] DevTools WebSocket Response: Runtime.evaluate (id=25) (session_id=83FA31A08373B5CABD189FDA565F17A4) D3674DA1CAAEC7ECD26D687E55D0BE74 {
   "result": {
      "description": "1",
      "type": "number",
      "value": 1
   }
}

Jibri 2025-07-08 11:25:33.666 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T11:25:33+0000] [INFO] Done waiting for pending navigations. Status: ok

Jibri 2025-07-08 11:25:33.667 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T11:25:33+0000] [INFO] [e83271e33e707f07e91d440b1a834fa0] RESPONSE Navigate ERROR unknown error: net::ERR_CONNECTION_REFUSED
  (Session info: chrome=130.0.6723.116)

Jibri 2025-07-08 11:25:33.667 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T11:25:33+0000] [INFO] [e83271e33e707f07e91d440b1a834fa0] COMMAND GetLogTypes {
}

Jibri 2025-07-08 11:25:33.667 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T11:25:33+0000] [INFO] [e83271e33e707f07e91d440b1a834fa0] RESPONSE GetLogTypes [ "browser", "driver" ]

Jibri 2025-07-08 11:25:33.667 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T11:25:33+0000] [INFO] [e83271e33e707f07e91d440b1a834fa0] COMMAND GetLog {
   "type": "browser"
}

Jibri 2025-07-08 11:25:33.667 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T11:25:33+0000] [FINE] DevTools WebSocket Command: Runtime.evaluate (id=26) (session_id=83FA31A08373B5CABD189FDA565F17A4) D3674DA1CAAEC7ECD26D687E55D0BE74 {
   "awaitPromise": false,
   "expression": "1",
   "returnByValue": true
}

Jibri 2025-07-08 11:25:33.668 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T11:25:33+0000] [FINE] DevTools WebSocket Response: Runtime.evaluate (id=26) (session_id=83FA31A08373B5CABD189FDA565F17A4) D3674DA1CAAEC7ECD26D687E55D0BE74 {
   "result": {
      "description": "1",
      "type": "number",
      "value": 1
   }
}

Jibri 2025-07-08 11:25:33.668 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T11:25:33+0000] [INFO] [e83271e33e707f07e91d440b1a834fa0] RESPONSE GetLog [  ]

Jibri 2025-07-08 11:25:33.668 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T11:25:33+0000] [INFO] [e83271e33e707f07e91d440b1a834fa0] COMMAND GetLog {
   "type": "driver"
}

Jibri 2025-07-08 11:25:33.668 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T11:25:33+0000] [FINE] DevTools WebSocket Command: Runtime.evaluate (id=27) (session_id=83FA31A08373B5CABD189FDA565F17A4) D3674DA1CAAEC7ECD26D687E55D0BE74 {
   "awaitPromise": false,
   "expression": "1",
   "returnByValue": true
}

Jibri 2025-07-08 11:25:33.668 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#356: [2025-07-08T11:25:33+0000] [FINE] DevTools WebSocket Response: Runtime.evaluate (id=27) (session_id=83FA31A08373B5CABD189FDA565F17A4) D3674DA1CAAEC7ECD26D687E55D0BE74 {
   "result": {
      "description": "1",
      "type": "number",
      "value": 1
   }
}

Jibri 2025-07-08 11:25:33.669 INFO: [61] JibriSelenium.leaveCallAndQuitBrowser#354: ========= TYPE=client ===========
