




jicofo {
    

    // Configuration related to jitsi-videobridge
    bridge {
      

      

      

      
      
      

      

      
      

      
      brewery-jid = "<EMAIL>"
      

      
    }
    // Configure the codecs and RTP extensions to be used in the offer sent to clients.
    codec {
      video {
        
        
        
        
      }
      audio {
        
      }
      rtp-extensions {
        video-layers-allocation {
          enabled = false
        }
      }
    }

    conference {
      

      

      

      max-ssrcs-per-user = "20"

      max-ssrc-groups-per-user = "20"

      

      

      

      

      

      

      

      

      
    }

    

    
    jibri {
      brewery-jid = "<EMAIL>"
      
      pending-timeout = "90 seconds"
    }
    

    

    

    

    octo {
      // Whether or not to use Octo. Note that when enabled, its use will be determined by
      // $jicofo.bridge.selection-strategy. There's a corresponding flag in the JVB and these
      // two MUST be in sync (otherwise bridges will crash because they won't know how to
      // deal with octo channels).
      enabled = false
      sctp-datachannels = true
    }

    

    sctp {
      enabled = true
    }

    xmpp {
      
      client {
        enabled = true
        hostname = "xmpp.meet.jitsi"
        port = "5222"
        domain = "auth.localhost"
        xmpp-domain = "localhost"
        username = "focus"
        password = "1b0dadb48f5a32cf619086a076712f13"
        conference-muc-jid = "muc.localhost"
        client-proxy = "focus.localhost"
        disable-certificate-verification = true
      }
      

      trusted-domains = [ "recorder.localhost" ]

    }
}

include "custom-jicofo.conf"
