

videobridge {
    cc {
        use-vla-target-bitrate = false
        trust-bwe = true
    }
    ice {
        udp {
            port = 10000
        }
        advertise-private-candidates = true
    }
    apis {
xmpp-client {
            configs {


                shard0 {
                    HOSTNAME = "xmpp.meet.jitsi"
                    PORT = "5222"
                    DOMAIN = "auth.localhost"
                    USERNAME = "jvb"
                    PASSWORD = "8a98b01486851ae9bed35efe7b94cdf1"
                    MUC_JIDS = "<EMAIL>"
                    MUC_NICKNAME = "abae20a47106"
                    DISABLE_CERTIFICATE_VERIFICATION = true
                }
}
        }
rest {
            enabled = false
        }
    }
    rest {
        shutdown {
            enabled = false
        }
    }
    sctp {
      enabled = true
      use-usrsctp = false
    }
    stats {
        enabled = true
    }
    websockets {
        enabled = false
        domain = "localhost:8443"
        tls = true
        server-id = "**********"
    }
    http-servers {
        private {
          host = 0.0.0.0
          send-server-version = false
        }
        public {
            host = 0.0.0.0
            port = 9090
            send-server-version = false
        }
    }
    health {
        require-valid-address = false
    }

    }

ice4j {
    harvest {
        mapping {
            stun {
addresses = [ "meet-jit-si-turnrelay.jitsi.net:443" ]
}
            static-mappings = [
]
        }
    }
}

include "custom-jvb.conf"
