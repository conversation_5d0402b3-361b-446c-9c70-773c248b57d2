services:
    # Frontend
    web:
        image: jitsi/web:${JITSI_IMAGE_VERSION}
        restart: ${RESTART_POLICY:-unless-stopped}
        ports:
            - '${HTTP_PORT}:80'
            - '${HTTPS_PORT}:443'
        # extra_hosts:
        #     - "meet.jitsi.local:**************"
        # extra_hosts:
        #     - "localhost:**********"

     
        volumes:
            # - ./config/web/config.js:/config/config.js:ro
            - ${CONFIG}/web:/config:Z
            - ${CONFIG}/web/crontabs:/var/spool/cron/crontabs:Z
            - ${CONFIG}/transcripts:/usr/share/jitsi-meet/transcripts:Z
            - ${CONFIG}/web/load-test:/usr/share/jitsi-meet/load-test:Z
        labels:
            service: "jitsi-web"
        environment:
            - AMPLITUDE_ID
            - ANALYTICS_SCRIPT_URLS
            - ANALYTICS_WHITELISTED_EVENTS
            - AUDIO_QUALITY_OPUS_BITRATE
            - AUTO_CAPTION_ON_RECORD
            - BRANDING_DATA_URL
            - BOSH_RELATIVE
            - CHROME_EXTENSION_BANNER_JSON
            - CODEC_ORDER_JVB
            - CODEC_ORDER_JVB_MOBILE
            - CODEC_ORDER_P2P
            - CODEC_ORDER_P2P_MOBILE
            - COLIBRI_WEBSOCKET_PORT
            - COLIBRI_WEBSOCKET_JVB_LOOKUP_NAME
            - COLIBRI_WEBSOCKET_REGEX
            - CONFCODE_URL
            - CORS_HEADER_ACCESS_CONTROL_ALLOW_ORIGIN
            - DEFAULT_LANGUAGE
            - DEPLOYMENTINFO_ENVIRONMENT
            - DEPLOYMENTINFO_ENVIRONMENT_TYPE
            - DEPLOYMENTINFO_REGION
            - DEPLOYMENTINFO_SHARD
            - DESKTOP_SHARING_FRAMERATE_AUTO
            - DESKTOP_SHARING_FRAMERATE_MIN
            - DESKTOP_SHARING_FRAMERATE_MAX
            - DIALIN_NUMBERS_URL
            - DIALOUT_AUTH_URL
            - DIALOUT_CODES_URL
            - DISABLE_AUDIO_LEVELS
            - DISABLE_COLIBRI_WEBSOCKET_JVB_LOOKUP
            - DISABLE_DEEP_LINKING
            - DISABLE_GRANT_MODERATOR
            - DISABLE_HTTPS
            - DISABLE_KICKOUT
            - DISABLE_LOCAL_RECORDING
            - DISABLE_POLLS
            - DISABLE_PRIVATE_CHAT
            - DISABLE_PROFILE
            - DISABLE_REACTIONS
            - DISABLE_REMOTE_VIDEO_MENU
            - DISABLE_START_FOR_ALL
            - DROPBOX_APPKEY
            - DROPBOX_REDIRECT_URI
            - DYNAMIC_BRANDING_URL
            - ENABLE_ADAPTIVE_MODE
            - ENABLE_AUDIO_PROCESSING
            - ENABLE_AUTOMATIC_GAIN_CONTROL
            - ENABLE_AUTH
            - ENABLE_AUTH_DOMAIN
            - ENABLE_BREAKOUT_ROOMS
            - ENABLE_CALENDAR
            - ENABLE_COLIBRI_WEBSOCKET
            - ENABLE_COLIBRI_WEBSOCKET_UNSAFE_REGEX
            - ENABLE_E2EPING
            - ENABLE_FILE_RECORDING_SHARING
            - ENABLE_GUESTS
            - ENABLE_HSTS
            - ENABLE_HTTP_REDIRECT
            - ENABLE_IPV6
            - ENABLE_LETSENCRYPT
            - ENABLE_NO_AUDIO_DETECTION
            - ENABLE_NOISY_MIC_DETECTION
            - ENABLE_OCTO
            - ENABLE_OPUS_RED
            - ENABLE_PREJOIN_PAGE
            - ENABLE_P2P
            - ENABLE_WELCOME_PAGE
            - ENABLE_CLOSE_PAGE
            - ENABLE_LIVESTREAMING
            - ENABLE_LIVESTREAMING_DATA_PRIVACY_LINK
            - ENABLE_LIVESTREAMING_HELP_LINK
            - ENABLE_LIVESTREAMING_TERMS_LINK
            - ENABLE_LIVESTREAMING_VALIDATOR_REGEXP_STRING
            - ENABLE_LOAD_TEST_CLIENT
            - ENABLE_LOCAL_RECORDING_NOTIFY_ALL_PARTICIPANT
            - ENABLE_LOCAL_RECORDING_SELF_START
            - ENABLE_RECORDING
            - ENABLE_REMB
            - ENABLE_REQUIRE_DISPLAY_NAME
            - ENABLE_SERVICE_RECORDING
            - ENABLE_SIMULCAST
            - ENABLE_STATS_ID
            - ENABLE_STEREO
            - ENABLE_SUBDOMAINS
            - ENABLE_TALK_WHILE_MUTED
            - ENABLE_TCC
            - ENABLE_TRANSCRIPTIONS
            - ENABLE_VLA
            - ENABLE_XMPP_WEBSOCKET
            - ENABLE_JAAS_COMPONENTS
            - ETHERPAD_PUBLIC_URL
            - ETHERPAD_URL_BASE
            - E2EPING_NUM_REQUESTS
            - E2EPING_MAX_CONFERENCE_SIZE
            - E2EPING_MAX_MESSAGE_PER_SECOND
            - GOOGLE_ANALYTICS_ID
            - GOOGLE_API_APP_CLIENT_ID
            - HIDE_PREMEETING_BUTTONS
            - HIDE_PREJOIN_DISPLAY_NAME
            - HIDE_PREJOIN_EXTRA_BUTTONS
            - INVITE_SERVICE_URL
            - JVB_PREFER_SCTP
            - LETSENCRYPT_DOMAIN
            - LETSENCRYPT_EMAIL
            - LETSENCRYPT_USE_STAGING
            - LETSENCRYPT_ACME_SERVER
            - MATOMO_ENDPOINT
            - MATOMO_SITE_ID
            - MICROSOFT_API_APP_CLIENT_ID
            - NGINX_KEEPALIVE_TIMEOUT
            - NGINX_RESOLVER
            - NGINX_WORKER_PROCESSES
            - NGINX_WORKER_CONNECTIONS
            - PEOPLE_SEARCH_URL
            - PREFERRED_LANGUAGE
            - PUBLIC_URL
            - P2P_PREFERRED_CODEC
            - P2P_STUN_SERVERS
            - RESOLUTION
            - RESOLUTION_MIN
            - RESOLUTION_WIDTH
            - RESOLUTION_WIDTH_MIN
            - START_AUDIO_MUTED
            - START_AUDIO_ONLY
            - START_SILENT
            - START_WITH_AUDIO_MUTED
            - START_VIDEO_MUTED
            - START_WITH_VIDEO_MUTED
            - TOKEN_AUTH_URL
            - TOOLBAR_BUTTONS
            - TRANSLATION_LANGUAGES
            - TRANSLATION_LANGUAGES_HEAD
            - TZ
            - USE_APP_LANGUAGE
            - VIDEOQUALITY_BITRATE_H264_LOW
            - VIDEOQUALITY_BITRATE_H264_STANDARD
            - VIDEOQUALITY_BITRATE_H264_HIGH
            - VIDEOQUALITY_BITRATE_H264_FULL
            - VIDEOQUALITY_BITRATE_H264_ULTRA
            - VIDEOQUALITY_BITRATE_H264_SS_HIGH
            - VIDEOQUALITY_BITRATE_VP8_LOW
            - VIDEOQUALITY_BITRATE_VP8_STANDARD
            - VIDEOQUALITY_BITRATE_VP8_HIGH
            - VIDEOQUALITY_BITRATE_VP8_FULL
            - VIDEOQUALITY_BITRATE_VP8_ULTRA
            - VIDEOQUALITY_BITRATE_VP8_SS_HIGH
            - VIDEOQUALITY_BITRATE_VP9_LOW
            - VIDEOQUALITY_BITRATE_VP9_STANDARD
            - VIDEOQUALITY_BITRATE_VP9_HIGH
            - VIDEOQUALITY_BITRATE_VP9_FULL
            - VIDEOQUALITY_BITRATE_VP9_ULTRA
            - VIDEOQUALITY_BITRATE_VP9_SS_HIGH
            - VIDEOQUALITY_BITRATE_AV1_LOW
            - VIDEOQUALITY_BITRATE_AV1_STANDARD
            - VIDEOQUALITY_BITRATE_AV1_HIGH
            - VIDEOQUALITY_BITRATE_AV1_FULL
            - VIDEOQUALITY_BITRATE_AV1_ULTRA
            - VIDEOQUALITY_BITRATE_AV1_SS_HIGH
            - VIDEOQUALITY_PREFERRED_CODEC
            - XMPP_AUTH_DOMAIN
            - XMPP_BOSH_URL_BASE
            - XMPP_DOMAIN
            - XMPP_GUEST_DOMAIN
            - XMPP_MUC_DOMAIN
            - XMPP_HIDDEN_DOMAIN
            - XMPP_PORT
            - XMPP_RECORDER_DOMAIN
            - WHITEBOARD_COLLAB_SERVER_PUBLIC_URL
            - WHITEBOARD_COLLAB_SERVER_URL_BASE
        networks:
            meet.jitsi:
        depends_on:
            - jvb

    # XMPP server
    prosody:
        image: jitsi/prosody:${JITSI_IMAGE_VERSION}
        restart: ${RESTART_POLICY:-unless-stopped}
        expose:
            - '${XMPP_PORT:-5222}'
            - '${PROSODY_S2S_PORT:-5269}'
            - '5347'
            - '${PROSODY_HTTP_PORT:-5280}'
        labels:
            service: "jitsi-prosody"
        # extra_hosts:
        #     - "meet.jitsi.local:**************"
        # extra_hosts:
        #     - "localhost:**********"   
        volumes:
            - ${CONFIG}/prosody/config:/config:Z
            - ${CONFIG}/prosody/prosody-plugins-custom:/prosody-plugins-custom:Z
            - ${CONFIG}/prosody/conf.d:/config/conf.d:Z
            - ./config/prosody.cfg.lua:/etc/prosody/prosody.cfg.lua:ro
            - ./config/prosody:/etc/prosody/conf.d:ro
            - ./config/certs:/etc/prosody/certs:ro

        environment:
            - AUTH_TYPE
            - DISABLE_POLLS
            - ENABLE_AUTH
            - ENABLE_AV_MODERATION
            - ENABLE_BREAKOUT_ROOMS
            - ENABLE_END_CONFERENCE
            - ENABLE_GUESTS
            - ENABLE_IPV6
            - ENABLE_LOBBY
            - ENABLE_RECORDING
            - ENABLE_S2S
            - ENABLE_TRANSCRIPTIONS
            - ENABLE_VISITORS
            - ENABLE_XMPP_WEBSOCKET
            - ENABLE_JAAS_COMPONENTS
            - GC_TYPE
            - GC_INC_TH
            - GC_INC_SPEED
            - GC_INC_STEP_SIZE
            - GC_GEN_MIN_TH
            - GC_GEN_MAX_TH
            - GLOBAL_CONFIG
            - GLOBAL_MODULES
            - JIBRI_RECORDER_USER
            - JIBRI_RECORDER_PASSWORD
            - JIBRI_SIP_BREWERY_MUC
            - JIBRI_XMPP_USER
            - JIBRI_XMPP_PASSWORD
            - JICOFO_AUTH_PASSWORD
            - JICOFO_COMPONENT_SECRET
            - JIGASI_TRANSCRIBER_PASSWORD
            - JIGASI_TRANSCRIBER_USER
            - JIGASI_XMPP_USER
            - JIGASI_XMPP_PASSWORD
            - JVB_AUTH_USER
            - JVB_AUTH_PASSWORD
            - JWT_APP_ID
            - JWT_APP_SECRET
            - JWT_ACCEPTED_ISSUERS
            - JWT_ACCEPTED_AUDIENCES
            - JWT_ASAP_KEYSERVER
            - JWT_ALLOW_EMPTY
            - JWT_AUTH_TYPE
            - JWT_ENABLE_DOMAIN_VERIFICATION
            - JWT_SIGN_TYPE
            - JWT_TOKEN_AUTH_MODULE
            - MATRIX_UVS_URL
            - MATRIX_UVS_ISSUER
            - MATRIX_UVS_AUTH_TOKEN
            - MATRIX_UVS_SYNC_POWER_LEVELS
            - MATRIX_LOBBY_BYPASS
            - LOG_LEVEL
            - LDAP_AUTH_METHOD
            - LDAP_BASE
            - LDAP_BINDDN
            - LDAP_BINDPW
            - LDAP_FILTER
            - LDAP_VERSION
            - LDAP_TLS_CIPHERS
            - LDAP_TLS_CHECK_PEER
            - LDAP_TLS_CACERT_FILE
            - LDAP_TLS_CACERT_DIR
            - LDAP_START_TLS
            - LDAP_URL
            - LDAP_USE_TLS
            - MAX_PARTICIPANTS
            - PROSODY_ADMINS
            - PROSODY_AUTH_TYPE
            - PROSODY_C2S_LIMIT
            - PROSODY_C2S_REQUIRE_ENCRYPTION
            - PROSODY_RESERVATION_ENABLED
            - PROSODY_RESERVATION_REST_BASE_URL
            - PROSODY_DISABLE_C2S_LIMIT
            - PROSODY_DISABLE_S2S_LIMIT
            - PROSODY_ENABLE_FILTER_MESSAGES
            - PROSODY_ENABLE_RATE_LIMITS
            - PROSODY_ENABLE_RECORDING_METADATA
            - PROSODY_ENABLE_STANZA_COUNTS
            - PROSODY_ENABLE_S2S
            - PROSODY_ENABLE_METRICS
            - PROSODY_GUEST_AUTH_TYPE
            - PROSODY_HTTP_PORT
            - PROSODY_LOG_CONFIG
            - PROSODY_METRICS_ALLOWED_CIDR
            - PROSODY_MODE
            - PROSODY_RATE_LIMIT_LOGIN_RATE
            - PROSODY_RATE_LIMIT_SESSION_RATE
            - PROSODY_RATE_LIMIT_TIMEOUT
            - PROSODY_RATE_LIMIT_ALLOW_RANGES
            - PROSODY_RATE_LIMIT_CACHE_SIZE
            - PROSODY_S2S_LIMIT
            - PROSODY_S2S_PORT
            - PROSODY_TRUSTED_PROXIES
            - PROSODY_VISITOR_INDEX
            - PROSODY_VISITORS_MUC_PREFIX
            - PROSODY_VISITORS_S2S_VHOSTS
            - PUBLIC_URL
            - STUN_HOST
            - STUN_PORT
            - TURN_CREDENTIALS
            - TURN_USERNAME
            - TURN_PASSWORD
            - TURN_HOST
            - TURNS_HOST
            - TURN_PORT
            - TURNS_PORT
            - TURN_TRANSPORT
            - TURN_TTL
            - TZ
            - VISITORS_MAX_VISITORS_PER_NODE
            - VISITORS_XMPP_DOMAIN
            - VISITORS_XMPP_SERVER
            - VISITORS_XMPP_PORT
            - XMPP_BREAKOUT_MUC_MODULES
            - XMPP_CONFIGURATION
            - XMPP_DOMAIN
            - XMPP_AUTH_DOMAIN
            - XMPP_GUEST_DOMAIN
            - XMPP_MUC_DOMAIN
            - XMPP_INTERNAL_MUC_DOMAIN
            - XMPP_LOBBY_MUC_MODULES
            - XMPP_MODULES
            - XMPP_MUC_MODULES
            - XMPP_MUC_CONFIGURATION
            - XMPP_INTERNAL_MUC_MODULES
            - XMPP_HIDDEN_DOMAIN
            - XMPP_PORT
            - XMPP_RECORDER_DOMAIN
            - XMPP_SERVER_S2S_PORT
            - XMPP_SPEAKERSTATS_MODULES
        networks:
            meet.jitsi:
                aliases:
                    - ${XMPP_SERVER:-xmpp.meet.jitsi}

    # Focus component
    jicofo:
        image: jitsi/jicofo:${JITSI_IMAGE_VERSION}
        restart: ${RESTART_POLICY:-unless-stopped}
        ports:
            - '127.0.0.1:${JICOFO_REST_PORT:-8888}:8888'
        # extra_hosts:
        #     - "meet.jitsi.local:**************"
        # extra_hosts:
        #     - "localhost:**********"    
        volumes:
            - ${CONFIG}/jicofo:/config:Z
        labels:
            service: "jitsi-jicofo"
        environment:
            - AUTH_TYPE
            - BRIDGE_AVG_PARTICIPANT_STRESS
            - BRIDGE_STRESS_THRESHOLD
            - ENABLE_AUTH
            - ENABLE_AUTO_OWNER
            - ENABLE_MODERATOR_CHECKS
            - ENABLE_CODEC_VP8
            - ENABLE_CODEC_VP9
            - ENABLE_CODEC_AV1
            - ENABLE_CODEC_H264
            - ENABLE_CODEC_OPUS_RED
            - ENABLE_JVB_XMPP_SERVER
            - ENABLE_OCTO
            - ENABLE_OCTO_SCTP
            - ENABLE_RECORDING
            - ENABLE_SCTP
            - ENABLE_SHARED_DOCUMENT_RANDOM_NAME
            - ENABLE_TRANSCRIPTIONS
            - ENABLE_VISITORS
            - ENABLE_AUTO_LOGIN
            - JICOFO_AUTH_LIFETIME
            - JICOFO_AUTH_PASSWORD
            - JICOFO_AUTH_TYPE
            - JICOFO_BRIDGE_REGION_GROUPS
            - JICOFO_ENABLE_AUTH
            - JICOFO_ENABLE_BRIDGE_HEALTH_CHECKS
            - JICOFO_CONF_INITIAL_PARTICIPANT_WAIT_TIMEOUT
            - JICOFO_CONF_SINGLE_PARTICIPANT_TIMEOUT
            - JICOFO_CONF_SOURCE_SIGNALING_DELAYS
            - JICOFO_CONF_MAX_AUDIO_SENDERS
            - JICOFO_CONF_MAX_VIDEO_SENDERS
            - JICOFO_CONF_STRIP_SIMULCAST
            - JICOFO_CONF_SSRC_REWRITING
            - JICOFO_ENABLE_HEALTH_CHECKS
            - JICOFO_ENABLE_ICE_FAILURE_DETECTION
            - JICOFO_ENABLE_LOAD_REDISTRIBUTION
            - JICOFO_ENABLE_REST
            - JICOFO_HEALTH_CHECKS_USE_PRESENCE
            - JICOFO_ICE_FAILURE_INTERVAL
            - JICOFO_ICE_FAILURE_MIN_ENDPOINTS
            - JICOFO_ICE_FAILURE_THRESHOLD
            - JICOFO_MAX_MEMORY
            - JICOFO_MULTI_STREAM_BACKWARD_COMPAT
            - JICOFO_OCTO_REGION
            - JICOFO_RESTART_REQUEST_MAX
            - JICOFO_RESTART_REQUEST_INTERVAL
            - JICOFO_TRUSTED_DOMAINS
            - JIBRI_BREWERY_MUC
            - JIBRI_REQUEST_RETRIES
            - JIBRI_PENDING_TIMEOUT
            - JIGASI_BREWERY_MUC
            - JIGASI_SIP_URI
            - JIGASI_TRUSTED_DOMAINS
            - JVB_BREWERY_MUC
            - JVB_XMPP_AUTH_DOMAIN
            - JVB_XMPP_INTERNAL_MUC_DOMAIN
            - JVB_XMPP_PORT
            - JVB_XMPP_SERVER
            - MAX_BRIDGE_PARTICIPANTS
            - OCTO_BRIDGE_SELECTION_STRATEGY
            - PROSODY_VISITORS_MUC_PREFIX
            - SENTRY_DSN="${JICOFO_SENTRY_DSN:-0}"
            - SENTRY_ENVIRONMENT
            - SENTRY_RELEASE
            - TZ
            - VISITORS_MAX_PARTICIPANTS
            - VISITORS_MAX_VISITORS_PER_NODE
            - VISITORS_XMPP_AUTH_DOMAIN
            - VISITORS_XMPP_SERVER
            - VISITORS_XMPP_DOMAIN
            - XMPP_DOMAIN
            - XMPP_AUTH_DOMAIN
            - XMPP_INTERNAL_MUC_DOMAIN
            - XMPP_MUC_DOMAIN
            - XMPP_HIDDEN_DOMAIN
            - XMPP_SERVER
            - XMPP_PORT
            - XMPP_RECORDER_DOMAIN
            - MAX_SSRCS_PER_USER
            - MAX_SSRC_GROUPS_PER_USER
        depends_on:
            - prosody
        networks:
            meet.jitsi:

    # Video bridge
    jvb:
        image: jitsi/jvb:${JITSI_IMAGE_VERSION}
        restart: ${RESTART_POLICY:-unless-stopped}
        ports:
            - '${JVB_PORT:-10000}:${JVB_PORT:-10000}/udp'
            - '127.0.0.1:${JVB_COLIBRI_PORT:-8080}:8080'
        # extra_hosts:
        #     - "meet.jitsi.local:**************"
        # extra_hosts:
        #     - "localhost:**********"    
        volumes:
            - ${CONFIG}/jvb:/config:Z
        labels:
            service: "jitsi-jvb"
        environment:
            # - JVB_ADVERTISE_IP=127.0.0.1
            - AUTOSCALER_SIDECAR_KEY_FILE
            - AUTOSCALER_SIDECAR_KEY_ID
            - AUTOSCALER_SIDECAR_GROUP_NAME
            - AUTOSCALER_SIDECAR_HOST_ID
            - AUTOSCALER_SIDECAR_INSTANCE_ID
            - AUTOSCALER_SIDECAR_PORT
            - AUTOSCALER_SIDECAR_REGION
            - AUTOSCALER_SIDECAR_SHUTDOWN_POLLING_INTERVAL
            - AUTOSCALER_SIDECAR_STATS_POLLING_INTERVAL
            - DOCKER_HOST_ADDRESS
            - ENABLE_COLIBRI_WEBSOCKET
            - ENABLE_JVB_XMPP_SERVER
            - ENABLE_OCTO
            - ENABLE_SCTP
            - JVB_ADVERTISE_IPS
            - JVB_ADVERTISE_PRIVATE_CANDIDATES
            - JVB_AUTH_USER
            - JVB_AUTH_PASSWORD
            - JVB_BREWERY_MUC
            - JVB_CC_TRUST_BWE
            - JVB_DISABLE_STUN
            - JVB_DISABLE_XMPP
            - JVB_INSTANCE_ID
            - JVB_PORT
            - JVB_MUC_NICKNAME
            - JVB_STUN_SERVERS
            - JVB_LOG_FILE
            - JVB_OCTO_BIND_ADDRESS
            - JVB_OCTO_REGION
            - JVB_OCTO_RELAY_ID
            - JVB_REQUIRE_VALID_ADDRESS
            - JVB_USE_USRSCTP
            - JVB_WS_DOMAIN
            - JVB_WS_SERVER_ID
            - JVB_WS_TLS
            - JVB_XMPP_AUTH_DOMAIN
            - JVB_XMPP_INTERNAL_MUC_DOMAIN
            - JVB_XMPP_PORT
            - JVB_XMPP_SERVER
            - PUBLIC_URL
            - SENTRY_DSN="${JVB_SENTRY_DSN:-0}"
            - SENTRY_ENVIRONMENT
            - SENTRY_RELEASE
            - COLIBRI_REST_ENABLED
            - SHUTDOWN_REST_ENABLED
            - TZ
            - VIDEOBRIDGE_MAX_MEMORY
            - XMPP_AUTH_DOMAIN
            - XMPP_INTERNAL_MUC_DOMAIN
            - XMPP_SERVER
            - XMPP_PORT
        depends_on:
            - prosody
        networks:
            meet.jitsi:

# Custom network so all services can communicate using a FQDN
networks:
    meet.jitsi:
