

services:
    jibri:
        image: jitsi/jibri:${J<PERSON><PERSON>_IMAGE_VERSION}
        restart: ${RESTART_POLICY:-unless-stopped}
        volumes:
            # - ${CONFIG}/jibri:/config:Z
            # - ${CONFIG}/jibri/recordings:/config/recordings
            - ./config/jibri:/config:Z
            - ./config/jibri/recordings:/config/recordings
        shm_size: '3gb'
        cap_add:
            - SYS_ADMIN
        environment:
        
            # - JIBRI_CHROME_FLAGS=--use-fake-ui-for-media-stream,--start-maximized,--kiosk,--enabled,--autoplay-policy=no-user-gesture-required,--ignore-certificate-errors,--disable-web-security

            - AUTOSCALER_SIDECAR_KEY_FILE
            - AUTOSCALER_SIDECAR_KEY_ID
            - AUTOSCALER_SIDECAR_GROUP_NAME
            - AUTOSCALER_SIDECAR_HOST_ID
            - AUTOSCALER_SIDECAR_INSTANCE_ID
            - AUTOSCALER_SIDECAR_PORT
            - AUTOSCALER_SIDECAR_REGION
            - AUTOSCALER_SIDECAR_SHUTDOWN_POLLING_INTERVAL
            - AUTOSCALER_SIDECAR_STATS_POLLING_INTERVAL
            - AUTOSCALER_URL
            - CHROMIUM_FLAGS
            - DISPLAY=:0
            - ENABLE_STATS_D
            - IGNORE_CERTIFICATE_ERRORS
            - JIBRI_WEBHOOK_SUBSCRIBERS
            - JIBRI_INSTANCE_ID
            - JIBRI_ENABLE_PROMETHEUS
            - JIBRI_HTTP_API_EXTERNAL_PORT
            - JIBRI_HTTP_API_INTERNAL_PORT
            - JIBRI_RECORDING_RESOLUTION
            - JIBRI_RECORDING_VIDEO_ENCODE_PRESET_RECORDING
            - JIBRI_RECORDING_VIDEO_ENCODE_PRESET_STREAMING
            - JIBRI_RECORDING_CONSTANT_RATE_FACTOR
            - JIBRI_RECORDING_FRAMERATE
            - JIBRI_RECORDING_QUEUE_SIZE
            - JIBRI_RECORDING_STREAMING_MAX_BITRATE
            - JIBRI_USAGE_TIMEOUT
            - JIBRI_XMPP_USER
            - JIBRI_XMPP_PASSWORD
            - JIBRI_XORG_HORIZ_SYNC
            - JIBRI_XORG_VERT_REFRESH
            - JIBRI_BREWERY_MUC
            - JIBRI_RECORDER_USER
            - JIBRI_RECORDER_PASSWORD
            - JIBRI_RECORDING_DIR
            - JIBRI_FINALIZE_RECORDING_SCRIPT_PATH
            - JIBRI_STRIP_DOMAIN_JID
            - JIBRI_STATSD_HOST
            - JIBRI_STATSD_PORT
            - LOCAL_ADDRESS
            - PUBLIC_URL
            - TZ
            - XMPP_AUTH_DOMAIN
            - XMPP_DOMAIN
            - XMPP_INTERNAL_MUC_DOMAIN
            - XMPP_MUC_DOMAIN
            - XMPP_HIDDEN_DOMAIN
            - XMPP_SERVER
            - XMPP_PORT
            - XMPP_RECORDER_DOMAIN
            - XMPP_TRUST_ALL_CERTS
        depends_on:
            - jicofo
        # extra_hosts:
        #     - "meet.jitsi.local:**************"
        # extra_hosts:
        #     - "localhost:**********"


     
        networks:
            meet.jitsi:
